# Environment Configuration Guide

This document explains the organized structure of the `.env` file and all required environment variables.

## File Organization

The `.env` file is organized into logical sections for better maintainability and clarity:

### 1. Application Configuration
General application settings that affect overall behavior.

### 2. Primary Azure OpenAI Configuration  
Configuration for the main Azure OpenAI model used by the application.

### 3. Secondary Azure OpenAI Configuration
Configuration for the fallback Azure OpenAI model used when the primary model fails.

### 4. Authentication Configuration
Settings for Asgardeo token introspection and user authentication.

### 5. Copilot OAuth2 Configuration
OAuth2 client credentials for copilot-to-agent communication.

### 6. Tool Configuration
Settings for external tools and services used by the copilot.

### 7. Azure Cosmos DB Configuration
Database connection settings for conversation history storage.

## Required Environment Variables

### Application Configuration (1 variable)
```env
MAX_QUESTION_LENGTH=256
```

### Primary Azure OpenAI Configuration (6 variables)
```env
AZURE_OPENAI_API_KEY=your-primary-api-key
AZURE_OPENAI_SERVICE_URL=https://your-primary-account.openai.azure.com/
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o-2024-11-20
AZURE_OPENAI_MAX_RETRIES=0
TIKTOKEN_MODEL_NAME=gpt-4o
```

### Secondary Azure OpenAI Configuration (6 variables)
```env
AZURE_OPENAI_SECONDARY_API_KEY=your-secondary-api-key
AZURE_OPENAI_SECONDARY_SERVICE_URL=https://your-secondary-account.openai.azure.com/
AZURE_OPENAI_SECONDARY_API_VERSION=2025-01-01-preview
AZURE_OPENAI_SECONDARY_DEPLOYMENT_NAME=gpt-4o-2024-11-20
AZURE_OPENAI_SECONDARY_MAX_RETRIES=0
TIKTOKEN_SECONDARY_MODEL_NAME=gpt-4o
```

### Authentication Configuration (3 variables)
```env
ASGARDEO_INTROSPECTION_ENDPOINT=https://api.asgardeo.io/t/your-org/oauth2/introspect
ASGARDEO_INTROSPECTION_CLIENT_ID=your-introspection-client-id
ASGARDEO_INTROSPECTION_CLIENT_SECRET=your-introspection-client-secret
```

### Copilot OAuth2 Configuration (3 variables)
```env
COPILOT_APP_CLIENT_ID=your-copilot-client-id
COPILOT_APP_CLIENT_SECRET=your-copilot-client-secret
COPILOT_APP_TOKEN_ENDPOINT=https://api.asgardeo.io/t/your-org/oauth2/token
```

### Tool Configuration (2 variables)
```env
DOCS_ASSISTANT_ENABLED=True
DOCS_ASSISTANT_URL=https://your-docs-assistant-endpoint/docs-assistant
```

### Azure Cosmos DB Configuration (3 variables)
```env
COSMOS_DB_CONNECTION_STRING=AccountEndpoint=https://your-account.documents.azure.com:443/;AccountKey=your-key;
COSMOS_DB_DATABASE=your-database-name
COSMOS_DB_CONTAINER=conversation-history
```

## Total Configuration

- **Total Variables**: 24 required environment variables
- **Total Sections**: 7 logical groupings
- **File Size**: ~51 lines (including comments and spacing)

## Removed Variables

The following unused variables were removed during cleanup:

**Vector Database (Unused)**
- `ZILLIZ_CLOUD_URI`
- `ZILLIZ_CLOUD_API_KEY`
- `DOCS_COLLECTION`
- `RELEASES_COLLECTION`

**Duplicate Azure OpenAI (Redundant)**
- `AZURE_OPENAI_ENDPOINT` (duplicate of `AZURE_OPENAI_SERVICE_URL`)

**Copilot Legacy (Unused)**
- `CP_AZURE_OPENAI_ENDPOINT`
- `CP_AZURE_OPENAI_API_KEY`

**Proxy Configuration (Unused)**
- `PROXY_URL`
- `PROXY_TOKEN`
- `COLLECTION_NAME`
- `ORGANIZATION_ID`
- `PROXY_CONSUMER_KEY`
- `PROXY_CONSUMER_SECRET`

**Development Tokens (Unused)**
- `GITHUB_TOKEN`

## Benefits of Organization

✅ **Clear Structure**: Logical grouping makes it easy to find related settings  
✅ **Reduced Size**: Removed 14 unused variables (37% reduction)  
✅ **Better Maintainability**: Section headers make it easy to understand purpose  
✅ **No Duplication**: Eliminated redundant variables  
✅ **Complete Coverage**: All 24 required variables are present  
✅ **Proper Ordering**: Related configurations are grouped together  

## Validation

The configuration can be validated using the test script which checks:
- All required variables are present
- No unused variables remain
- Proper section organization
- Configuration loading works correctly

## Best Practices

1. **Keep sections together**: Don't mix variables from different sections
2. **Use descriptive comments**: Section headers explain the purpose
3. **Remove unused variables**: Don't keep variables "just in case"
4. **Validate regularly**: Run tests to ensure configuration is complete
5. **Document changes**: Update this guide when adding new variables

The organized `.env` file provides a clean, maintainable configuration structure that supports the application's current needs without unnecessary complexity.
