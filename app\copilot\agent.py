import asyncio
import logging
from typing import Sequence, Type, List, Union, AsyncGenerator, Dict, Tuple

from langchain.agents.output_parsers.openai_tools import OpenAIToolsAgentOutputParser
from langchain_community.callbacks.manager import get_openai_callback
from langchain_community.chat_message_histories.in_memory import Chat<PERSON><PERSON>ageH<PERSON>ory
from langchain_core.agents import Agent<PERSON><PERSON>, AgentFinish
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate
from langchain_openai import AzureChatOpenAI
from pydantic import BaseModel
from tiktoken import Encoding

from app.copilot.constants import INPUT_QUESTION_KEY, INPUT_CONVERSATION_HISTORY_KEY, MAX_HISTORY_PROMPT_SIZE
from app.copilot.exception import AgentException
from app.copilot.memory import ConversationSummaryMemory
from app.copilot.prompts import FINAL_RESPONSE_SYSTEM_PROMPT, AGENT_SYSTEM_PROMPT, AGENT_USER_PROMPT, \
    FINAL_RESPONSE_USER_PROMPT
from app.copilot.tools import ToolOutput, ToolContext, AsgardeoDocsAssistant, StatusEnum, \
    BaseTool
from app.model import CacheRecord
from app.utils import retry_manager


async def has_completed(tasks: List[asyncio.Task]):
    return all(task.done() for task in tasks)


async def register_task(generator: AsyncGenerator, queue: asyncio.Queue):
    async for result in generator:
        await queue.put(result)


async def generate_final_answer(llm: AzureChatOpenAI, question: str, tool_answers: List[ToolOutput],
                                history: List[BaseMessage]) -> AsyncGenerator[str, None]:
    conversation = []
    for tool_answer in tool_answers:
        content = tool_answer.content if tool_answer.status == StatusEnum.SUCCESS \
            else f'Unable to use {tool_answer.tool_name} due to an error.'
        conversation.append(f"##Information from {tool_answer.tool_name}\n{content}\n")
    conversation = '\n'.join(conversation)

    prompt = ChatPromptTemplate.from_messages([
        SystemMessagePromptTemplate.from_template(FINAL_RESPONSE_SYSTEM_PROMPT),
        *history,
        HumanMessagePromptTemplate.from_template(FINAL_RESPONSE_USER_PROMPT)
    ])

    chain = prompt | llm
    async for chunk in chain.astream({INPUT_QUESTION_KEY: question, INPUT_CONVERSATION_HISTORY_KEY: conversation}):
        yield chunk.content


async def get_conversation_history(llm, history: CacheRecord, encoder: Encoding) -> Tuple[List[BaseMessage], str]:
    messages = []
    summary = history.summary if history.summary else ""
    history = history.history

    if len(history) == 0:  # No history, return empty string.
        return [], ""

    # Add history until it reaches MAX_PROMPT_SIZE.
    _history = ChatMessageHistory()
    token_count = 0
    for record in history:
        token_count += len(encoder.encode(f"{record.question}. {record.answer}"))
        if token_count > MAX_HISTORY_PROMPT_SIZE:
            break
        messages.append(HumanMessage(record.question))
        messages.append(AIMessage(record.answer))

    _history.add_user_message(history[-1].question)
    _history.add_ai_message(history[-1].answer)
    with get_openai_callback() as cb:
        memory = await ConversationSummaryMemory.from_messages(
            llm=llm,
            buffer=summary,
            chat_memory=_history,
            return_messages=True
        )
    logging.debug(f"Summarized memory token count: {cb.total_tokens}")
    return messages, memory.buffer


class Agent:
    chat_llm: AzureChatOpenAI
    tools: Dict[str, Type[BaseTool]]
    answer: str

    def __init__(self, llm: AzureChatOpenAI, tools: Sequence[Type[BaseTool]], encoder: Encoding) -> None:
        self.chat_llm = llm
        self.encoder = encoder

        # Bind the tools to the llm.
        self.llm = llm.bind_tools(tools)
        self.tools = {tool.__name__: tool for tool in tools}

        # Initialize the answers list.
        self.answer = ''
        self.summary = ''

    async def update_answer(self, answer: str):
        self.answer = answer

    async def reason(self, question: str, history: List[BaseMessage], summary: str) -> Union[Dict[str, BaseTool], str]:
        # Create the initial prompt.
        prompt = ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(AGENT_SYSTEM_PROMPT),
            *history,
            HumanMessagePromptTemplate.from_template(AGENT_USER_PROMPT)])

        # Create the reasoning chain.
        chain = prompt | self.llm | OpenAIToolsAgentOutputParser()

        async def llm_reasoning_step() -> Union[str, Dict[str, Type[BaseTool]]]:
            # Run the reasoning chain.
            tool_actions: Union[List[AgentAction], AgentFinish] = await chain.ainvoke(
                {INPUT_QUESTION_KEY: question, INPUT_CONVERSATION_HISTORY_KEY: summary}
            )

            # Check if the we have a direct response from the agent.
            if isinstance(tool_actions, AgentFinish):
                agent_response = tool_actions.return_values.get("output")
                if agent_response:
                    logging.debug(f"Final Response: {agent_response}")
                    return agent_response
                else:
                    logging.warning("No final response from the agent for the question : {question}.")
                    return "I am sorry, I could not find an answer to your question."

            # Check if the reasoning chain has any tool actions to execute.
            if not tool_actions:
                raise Exception("No tool actions found in the reasoning chain.")

            # Create a list of actions out of the tool actions.
            tools = {}
            for action in tool_actions:
                logging.debug(f"Suggested tool: {action.tool} with inputs: {action.tool_input}")
                tools[action.tool_call_id] = self.tools.get(action.tool)(**action.tool_input)
            return tools

        return await retry_manager(llm_reasoning_step)

    async def execute(self, question: str, context: ToolContext) -> AsyncGenerator[str, None]:
        history, summary = await get_conversation_history(self.chat_llm, context.history, self.encoder)
        # Execute the reasoning chain.
        try:
            tool_actions: Union[Dict[str, BaseTool], str] = await self.reason(question, history, summary)
        except Exception:
            raise AgentException("Copilot is failing to answer due to an LLM error. Please retry.")

        self.summary = summary
        if isinstance(tool_actions, str):
            await self.update_answer(tool_actions)
            yield tool_actions
            return

        # Execute the tools.
        tool_answers: List[ToolOutput] = []

        # Check if we can use streaming to answer.
        try:
            if len(tool_actions) == 1:
                tool_call_id, tool = next(iter(tool_actions.items()))
                if (isinstance(tool, AsgardeoDocsAssistant)):
                    yield await tool.message(tool_call_id)
                    answer = ""
                    async for chunk in tool.run(tool_call_id, context, is_stream=True):
                        if chunk.status == StatusEnum.STREAM:
                            answer += chunk.content
                            yield chunk.content
                        else:
                            logging.error(
                                f"{context.session_id} - Error while streaming the response: {chunk.content}.")
                            raise AgentException("Copilot is failing to answer due to an error. Please retry.")
                    await self.update_answer(answer)
                    return
        except Exception:
            raise AgentException("Copilot is failing to answer due to an error. Please retry.")

        # Queue the tasks to be executed.
        queue = asyncio.Queue()
        tasks = []
        for tool_call_id, tool in tool_actions.items():
            yield await tool.message(tool_call_id)
            task = asyncio.create_task(register_task(tool.run(tool_call_id, context), queue))
            tasks.append(task)

        # Start executing the tools asynchronously.
        # Returns the partial results as they are available.
        try:
            while True:
                if queue.empty() and await has_completed(tasks):
                    break
                result = await queue.get()
                # If the tool has a response, send the user with the status.
                if isinstance(result, ToolOutput):
                    logging.debug(f"Response from tool '{result.tool_name}': {result.content}")

                    if result.status == StatusEnum.FAILURE:
                        raise AgentException("Copilot is failing to answer due to an error. Please retry.")

                    # Send the assistant call completion status.
                    content: Union[BaseModel, str] = result.content
                    if isinstance(content, str):
                        tool_answers.append(result)
                    else:
                        raise Exception(f"Invalid content type: {type(content)}")
                else:
                    raise Exception(f"Invalid tool output type: {type(result)}")
        except Exception:
            raise AgentException("Copilot is failing to answer due to an error. Please retry.")

        try:
            answer = ""
            if len(tool_answers) == 1:  # If only one tool was called, return its response.
                tool_answer = tool_answers[0]
                if tool_answer.status == StatusEnum.FAILURE:
                    answer = "I'm sorry. Due to an error, I'm unable to answer your question."
                else:
                    answer = tool_answers[0].content
                yield answer
            else:  # If multiple tools were called, generate the final response by combining the tool responses.
                async for chunk in generate_final_answer(self.chat_llm, question, tool_answers, history):
                    answer += chunk
                    yield chunk
            await self.update_answer(answer)
        except Exception:
            raise AgentException("Copilot is failing to answer due to an LLM error. Please retry.")
