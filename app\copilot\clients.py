from typing import As<PERSON><PERSON><PERSON><PERSON>, Optional, Dict, Union

import httpx

from app.config import get_config
from app.constants import BEARER_SCHEME, AUTHORIZATION_HEADER
from app.copilot.constants import POST_METHOD
from app.copilot.token_manager import get_token_manager

# Load configuration
config = get_config()

# Assistant service URLs.
docs_assistant_url = config.tools.docs_assistant.url

# OAuth2 token manager.
token_manager = get_token_manager()


def _is_agent_request(url: str) -> bool:
    """
    Check if the URL is for an agent that requires copilot authentication.

    Args:
        url: The request URL

    Returns:
        bool: True if this is an agent request that needs authentication
    """
    agent_indicators = [
        "docs-assistant",
    ]

    return any(indicator in url for indicator in agent_indicators)


async def make_stream_request(url: str, x_request_id: str, **kwargs) -> AsyncGenerator[str, None]:
    """
    Makes a streaming request to the given URL using the specified method.
    Uses retry mechanism for agent requests - if auth fails, refreshes token and retries.

    Args:
        url (str): The URL to make the request to.
        x_request_id (str): The request ID to include in the headers.
        **kwargs: Additional keyword arguments to pass to httpx client.

    Yields:
        str: The streaming response content.

    Raises:
        httpx.HTTPError: If the request fails after retry
    """
    is_agent_req = _is_agent_request(url)

    async def _make_single_stream_request(use_fresh_token: bool = False) -> AsyncGenerator[str, None]:
        headers = {config.constants.request_id_header_name: x_request_id}

        # Add OAuth2 token for agent requests.
        if is_agent_req:
            if use_fresh_token:
                # Get fresh token for retry.
                fresh_token = await token_manager.refresh_token()
                if fresh_token:
                    headers[AUTHORIZATION_HEADER] = f"{BEARER_SCHEME} {fresh_token}"
            else:
                # Use existing token.
                auth_header = await token_manager.get_authorization_header()
                if auth_header:
                    headers[AUTHORIZATION_HEADER] = auth_header

        kwargs["headers"] = headers
        async with httpx.AsyncClient(timeout=httpx.Timeout(60, read=120)) as client:
            async with client.stream(POST_METHOD, url, **kwargs) as response:
                response.raise_for_status()
                async for chunk in response.aiter_text():
                    yield chunk

    # First attempt.
    try:
        async for chunk in _make_single_stream_request():
            yield chunk
    except httpx.HTTPStatusError as e:
        # Only retry for agent requests with auth errors (401/403).
        if is_agent_req and e.response.status_code in [401, 403]:
            try:
                # Retry with fresh token.
                async for chunk in _make_single_stream_request(use_fresh_token=True):
                    yield chunk
            except Exception:
                # If retry also fails, raise the original error.
                raise e
        else:
            # Not an auth error or not an agent request, don't retry.
            raise e


async def make_request(url: str, x_request_id: str, params: Optional[Dict] = None, body: Optional[Dict] = None,
                       auth_token: str = None) -> Union[Dict, str]:
    """
    Makes a POST request to the given URL with optional query parameters and body.
    Uses retry mechanism for agent requests - if auth fails, refreshes token and retries.

    Args:
        url (str): The URL to make the request to.
        x_request_id (str): The request ID to include in the headers.
        params (Optional[Dict]): Query parameters for the request.
        body (Optional[Dict]): JSON body for the request.
        auth_token (str): Authorization token.

    Returns:
        str: The response text.

    Raises:
        httpx.HTTPError: If the request fails after retry
    """
    is_agent_req = _is_agent_request(url)

    async def _make_single_request(use_fresh_token: bool = False) -> Union[Dict, str]:
        async with httpx.AsyncClient(timeout=httpx.Timeout(60, read=120)) as client:
            headers = {config.constants.request_id_header_name: x_request_id}

            # Use OAuth2 token for agent requests, otherwise use provided auth_token.
            if is_agent_req:
                if use_fresh_token:
                    # Get fresh token for retry.
                    fresh_token = await token_manager.refresh_token()
                    if fresh_token:
                        headers[AUTHORIZATION_HEADER] = f"{BEARER_SCHEME} {fresh_token}"
                else:
                    # Use existing token.
                    auth_header = await token_manager.get_authorization_header()
                    if auth_header:
                        headers[AUTHORIZATION_HEADER] = auth_header
            elif auth_token:
                headers[AUTHORIZATION_HEADER] = f"{BEARER_SCHEME} {auth_token}"

            response = await client.post(url, json=body, params=params, headers=headers)
            response.raise_for_status()

            content_type = response.headers.get("Content-Type", "").lower()
            if "application/json" in content_type:
                response_data = response.json()
                if "content" in response_data:
                    return response_data.get("content")
                else:
                    return response_data
            else:
                return response.text

    # First attempt.
    try:
        return await _make_single_request()
    except httpx.HTTPStatusError as e:
        # Only retry for agent requests with auth errors (401/403).
        if is_agent_req and e.response.status_code in [401, 403]:
            try:
                # Retry with fresh token.
                return await _make_single_request(use_fresh_token=True)
            except Exception:
                # If retry also fails, raise the original error.
                raise e
        else:
            # Not an auth error or not an agent request, don't retry.
            raise e
