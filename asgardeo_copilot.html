<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asgardeo Copilot</title>
    <!-- Include Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- Include Highlight.js for code syntax highlighting -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/highlight.min.js"></script>
    <!-- Include Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #FF7200; /* Asgardeo Orange */
            --secondary-color: #FF9433; /* Lighter orange */
            --dark-color: #1a1a1a; /* Dark text */
            --background-color: #FFFFFF; /* White background */
            --text-color: #333333; /* Dark text for light background */
            --border-color: #E1E5E9; /* Light border */
            --success-color: #FF6600; /* Darker orange for success */
            --shadow-color: rgba(0, 0, 0, 0.08); /* Light shadow */
            --panel-background: #FAFBFC; /* Light panel background */
            --hover-color: #F4F5F7; /* Hover state */
            --muted-text: #6B778C; /* Muted text color */
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.5;
            height: 100vh;
            display: flex;
            flex-direction: column;
            font-size: 14px;
        }

        /* Copilot Panel Container */
        .copilot-panel {
            position: fixed;
            top: 0;
            right: 0;
            width: 600px;
            height: 100vh;
            background-color: var(--background-color);
            border-left: 1px solid var(--border-color);
            box-shadow: -2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            z-index: 1000;
            transform: translateX(0);
            transition: transform 0.3s ease;
        }

        .copilot-panel.collapsed {
            transform: translateX(100%);
        }

        .copilot-panel.collapsed ~ .copilot-toggle-btn {
            display: flex;
        }

        .copilot-panel.expanded {
            width: 100vw;
            left: 0;
            right: 0;
        }

        /* Panel Header */
        .panel-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            background-color: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            min-height: 64px;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .copilot-logo {
            width: 32px;
            height: 32px;
            border-radius: 6px;
        }

        .header-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark-color);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .header-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            color: var(--muted-text);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .header-button:hover {
            background-color: var(--hover-color);
            color: var(--text-color);
        }

        .header-button i {
            font-size: 14px;
        }

        /* Dropdown Menu */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: var(--background-color);
            min-width: 160px;
            box-shadow: 0 4px 12px var(--shadow-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            z-index: 1001;
            overflow: hidden;
        }

        .dropdown-content a {
            color: var(--text-color);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .dropdown-content a:hover {
            background-color: var(--hover-color);
        }

        .dropdown-content a i {
            font-size: 14px;
            width: 16px;
        }

        .dropdown.show .dropdown-content {
            display: block;
        }

        /* Copilot Toggle Button */
        .copilot-toggle-btn {
            position: fixed;
            bottom: 24px;
            right: 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(255, 114, 0, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 999;
            font-family: inherit;
        }

        .copilot-toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 114, 0, 0.4);
        }

        .copilot-toggle-btn i {
            font-size: 16px;
        }

        .copilot-toggle-btn.panel-open {
            background: var(--muted-text);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .copilot-toggle-btn.panel-open:hover {
            background: var(--text-color);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }



        /* Main Content Area */
        .panel-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Welcome Section */
        .welcome-section {
            padding: 48px 20px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .welcome-avatar {
            width: 112px;
            height: 112px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .welcome-avatar img {
            width: 112px;
            height: 112px;
            object-fit: contain;
        }

        .welcome-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .welcome-subtitle {
            font-size: 14px;
            color: var(--muted-text);
            line-height: 1.4;
        }

        /* Suggested Actions */
        .suggested-actions {
            padding: 20px;
        }

        .actions-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--muted-text);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .action-card {
            padding: 12px 16px;
            background-color: var(--background-color);
            border: 1px solid var(--primary-color);
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: var(--text-color);
        }

        .action-card:hover {
            background-color: var(--hover-color);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .action-card:last-child {
            margin-bottom: 0;
        }

        /* Chat Area */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for flex child to be scrollable */
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 16px 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
            min-height: 0; /* Important for flex child to be scrollable */
        }

        .message {
            display: flex;
            flex-direction: column;
            max-width: 90%;
        }

        .message-user {
            align-self: flex-end;
        }

        .message-assistant {
            align-self: flex-start;
        }

        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 12px;
            color: var(--muted-text);
        }

        .message-header img {
            width: 18px;
            height: 18px;
            margin-right: 8px;
            border-radius: 50%;
        }

        .message-time {
            margin-left: auto;
            font-size: 11px;
            color: var(--muted-text);
        }

        .message-content {
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .message-user .message-content {
            background-color: var(--primary-color);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message-assistant .message-content {
            background-color: var(--panel-background);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 4px;
        }



        /* Input Area */
        .input-area {
            padding: 16px 20px;
            background-color: var(--background-color);
        }

        .input-container {
            position: relative;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--background-color);
            transition: border-color 0.2s ease;
        }

        .input-container:focus-within {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 114, 0, 0.1);
        }

        .message-input {
            width: 100%;
            padding: 12px 48px 12px 16px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            resize: none;
            outline: none;
            background: transparent;
            color: var(--text-color);
            max-height: 120px;
            min-height: 44px;
            font-family: inherit;
        }

        .message-input::placeholder {
            color: var(--muted-text);
        }

        .send-button {
            position: absolute;
            right: 8px;
            bottom: 8px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .send-button:hover {
            background-color: var(--success-color);
            transform: scale(1.05);
        }

        .send-button:disabled {
            background-color: var(--muted-text);
            cursor: not-allowed;
            transform: none;
        }

        .send-button i {
            font-size: 12px;
        }

        .send-button.stop-mode {
            background-color: var(--primary-color);
        }

        .send-button.stop-mode:hover {
            background-color: var(--success-color);
        }

        /* Bottom Section (Input + Footer combined) */
        .bottom-section {
            border-top: 1px solid var(--border-color);
            background-color: var(--background-color);
        }

        /* Footer */
        .panel-footer {
            padding: 12px 20px;
            background-color: var(--panel-background);
            text-align: center;
        }

        .footer-text {
            font-size: 12px;
            color: var(--muted-text);
            line-height: 1.4;
        }

        .footer-link {
            color: var(--primary-color);
            text-decoration: none;
        }

        .footer-link:hover {
            text-decoration: underline;
        }

        /* Thinking Indicator (inline in chat) */
        .thinking-message {
            display: flex;
            flex-direction: column;
            max-width: 90%;
            align-self: flex-start;
        }

        .thinking-content {
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
            background-color: var(--panel-background);
            color: var(--muted-text);
            border: 1px solid var(--border-color);
            border-bottom-left-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 20px;
        }

        .typing-indicator {
            display: inline-flex;
            align-items: center;
        }

        .typing-indicator span {
            display: inline-block;
            width: 4px;
            height: 4px;
            background-color: var(--muted-text);
            border-radius: 50%;
            margin-right: 2px;
            animation: typing 1.4s infinite;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            40% {
                transform: translateY(-3px);
                opacity: 1;
            }
        }

        .status {
            padding: 8px 20px;
            font-size: 12px;
            color: var(--muted-text);
            text-align: center;
            background-color: var(--panel-background);
        }



        /* Markdown styling */
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            color: var(--dark-color);
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            line-height: 1.2;
        }

        .markdown-content h1 {
            font-size: 1.8em;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        .markdown-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 0.3em;
        }

        .markdown-content h3 {
            font-size: 1.3em;
        }

        .markdown-content p {
            margin: 0.8em 0;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin: 0.8em 0;
            padding-left: 2em;
        }

        .markdown-content li {
            margin: 0.3em 0;
        }

        .markdown-content code {
            background-color: rgba(255, 114, 0, 0.1);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-family: SFMono-Regular, Consolas, Liberation Mono, Menlo, monospace;
            font-size: 0.9em;
            color: var(--primary-color);
        }

        .markdown-content pre {
            background-color: #F5F5F5;
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
            margin: 1em 0;
            border-left: 3px solid var(--primary-color);
            position: relative;
        }

        .markdown-content pre code {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
            font-size: 0.9em;
            color: var(--text-color);
        }

        /* Code Block Copy Button */
        .code-copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            color: var(--muted-text);
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            z-index: 10;
            backdrop-filter: blur(4px);
        }

        .code-copy-button:hover {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .code-copy-button.copied {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .markdown-content pre:hover .code-copy-button {
            opacity: 1;
        }

        .code-copy-button i {
            font-size: 10px;
        }

        .markdown-content blockquote {
            border-left: 4px solid var(--primary-color);
            padding-left: 1em;
            margin: 1em 0;
            color: #666;
            background-color: rgba(255, 114, 0, 0.05);
            padding: 0.5em 1em;
            border-radius: 0 5px 5px 0;
        }

        .markdown-content hr {
            border: 0;
            border-top: 1px solid var(--border-color);
            margin: 1.5em 0;
        }

        .markdown-content a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .markdown-content a:hover {
            text-decoration: underline;
        }

        .markdown-content img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            margin: 1em 0;
        }

        .markdown-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .markdown-content th,
        .markdown-content td {
            border: 1px solid var(--border-color);
            padding: 0.5em;
            text-align: left;
        }

        .markdown-content th {
            background-color: rgba(255, 114, 0, 0.1);
            color: var(--dark-color);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .copilot-panel {
                width: 100vw;
                left: 0;
                right: 0;
            }

            .copilot-panel.collapsed {
                transform: translateX(100%);
            }

            .panel-header {
                padding: 12px 16px;
            }

            .welcome-section {
                padding: 32px 16px;
            }

            .suggested-actions {
                padding: 16px;
            }

            .chat-messages {
                padding: 12px 16px;
            }

            .input-area {
                padding: 12px 16px;
            }


        }

        @media (max-width: 480px) {
            .header-actions {
                gap: 4px;
            }

            .header-button {
                padding: 6px;
            }

            .welcome-title {
                font-size: 16px;
            }

            .welcome-subtitle {
                font-size: 13px;
            }

            .action-card {
                padding: 10px 12px;
                font-size: 13px;
            }

            .message-content {
                padding: 10px 12px;
                font-size: 13px;
            }
        }

        /* Custom Alert Modal - Inside Sidebar */
        .alert-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            border-radius: 12px;
        }

        .alert-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .alert-modal {
            background-color: var(--background-color);
            border-radius: 12px;
            padding: 24px;
            max-width: 320px;
            width: 85%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
            border: 1px solid var(--border-color);
            transform: scale(0.9);
            transition: transform 0.3s ease;
            margin: 20px;
        }

        .alert-overlay.show .alert-modal {
            transform: scale(1);
        }

        .alert-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .alert-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 20px;
        }

        .alert-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark-color);
            margin: 0;
        }

        .alert-message {
            font-size: 14px;
            color: var(--text-color);
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .alert-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .alert-button {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid var(--border-color);
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .alert-button:hover {
            background-color: var(--hover-color);
        }

        .alert-button.primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .alert-button.primary:hover {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
    </style>
</head>
<body>
    <!-- Copilot Toggle Button -->
    <button class="copilot-toggle-btn" id="copilot-toggle-btn" title="Toggle Copilot">
        <i class="fas fa-robot"></i>
        <span>Copilot</span>
    </button>

    <!-- Copilot Panel -->
    <div class="copilot-panel collapsed" id="copilot-panel">
        <!-- Panel Header -->
        <div class="panel-header">
            <div class="header-left">
                <img src="asgardeo-logo.png" alt="Asgardeo Copilot" class="copilot-logo">
                <span class="header-title">Copilot</span>
            </div>
            <div class="header-actions">
                <button class="header-button" id="clear-chat-btn" title="Clear Chat">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="header-button" id="expand-btn" title="Expand">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="header-button" id="close-btn" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>



        <!-- Panel Content -->
        <div class="panel-content">
            <!-- Welcome Section -->
            <div class="welcome-section" id="welcome-section">
                <div class="welcome-avatar">
                    <img src="asgardeo-copilot.svg" alt="Asgardeo Copilot">
                </div>
                <h2 class="welcome-title">Hi! How can I assist you with Asgardeo today?</h2>
                <p class="welcome-subtitle">I can help you with identity management, application setup, user management, and more.</p>
            </div>

            <!-- Suggested Actions -->
            <div class="suggested-actions" id="suggested-actions">
                <div class="actions-title">Try asking about</div>
                <div class="action-card" data-action="How can I create a new application in Asgardeo?">
                    How can I create a new application?
                </div>
                <div class="action-card" data-action="What are the different authentication methods available?">
                    Authentication methods available
                </div>
                <div class="action-card" data-action="How do I configure SAML SSO for my application?">
                    Configure SAML SSO
                </div>
                <div class="action-card" data-action="How can I manage user roles and permissions?">
                    Manage user roles and permissions
                </div>
            </div>

            <!-- Chat Area -->
            <div class="chat-area" id="chat-area" style="display: none;">
                <div id="chat-messages" class="chat-messages"></div>
                <div id="status" class="status"></div>


            </div>

        </div>

        <!-- Bottom Section (Input + Footer) -->
        <div class="bottom-section">
            <!-- Input Area -->
            <div class="input-area">
                <div class="input-container">
                    <textarea id="question" class="message-input" placeholder="Ask me anything about Asgardeo..."></textarea>
                    <button id="submit-btn" class="send-button">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                </div>
            </div>

            <!-- Panel Footer -->
            <div class="panel-footer">
                <div class="footer-text">
                    Use Copilot mindfully as AI can make mistakes.
                </div>
            </div>
        </div>

        <!-- Custom Alert Modal (Inside Sidebar) -->
        <div class="alert-overlay" id="alert-overlay">
            <div class="alert-modal">
                <div class="alert-header">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <h3 class="alert-title" id="alert-title">Confirm Action</h3>
                </div>
                <div class="alert-message" id="alert-message">
                    Are you sure you want to clear the chat? This action cannot be undone.
                </div>
                <div class="alert-actions">
                    <button class="alert-button" id="alert-cancel">Cancel</button>
                    <button class="alert-button primary" id="alert-confirm">Clear Chat</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Authentication helper class
        class AuthHelper {
            constructor() {
                this.updateAuthStatus();
            }

            // Get user opaque token from Asgardeo (hardcoded for testing)
            getAccessToken() {
                return '2c6a501c-9d16-3201-ab20-7fb210c041c5';
            }

            updateAuthStatus() {
                const statusElement = document.getElementById('status');
                if (statusElement) {
                    const token = this.getAccessToken();
                    statusElement.textContent = token ? 'Authenticated' : 'Not authenticated';
                }
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize the auth helper
            const authHelper = new AuthHelper();

            // Configure marked.js options
            marked.setOptions({
                breaks: true,  // Add line breaks on single line breaks.
                gfm: true,     // Enable GitHub Flavored Markdown.
                headerIds: true,
                highlight: function(code, lang) {
                    if (lang && hljs.getLanguage(lang)) {
                        return hljs.highlight(code, { language: lang }).value;
                    }
                    return hljs.highlightAuto(code).value;
                }
            });

            // UI Elements.
            const copilotPanel = document.getElementById('copilot-panel');
            const copilotToggleBtn = document.getElementById('copilot-toggle-btn');
            const questionInput = document.getElementById('question');
            const submitBtn = document.getElementById('submit-btn');
            const chatMessagesContainer = document.getElementById('chat-messages');
            const statusElement = document.getElementById('status');

            // New UI elements.
            const welcomeSection = document.getElementById('welcome-section');
            const suggestedActions = document.getElementById('suggested-actions');
            const chatArea = document.getElementById('chat-area');
            const actionCards = document.querySelectorAll('.action-card');
            const expandBtn = document.getElementById('expand-btn');
            const closeBtn = document.getElementById('close-btn');

            // Clear chat button.
            const clearChatBtn = document.getElementById('clear-chat-btn');

            // Custom alert elements.
            const alertOverlay = document.getElementById('alert-overlay');
            const alertTitle = document.getElementById('alert-title');
            const alertMessage = document.getElementById('alert-message');
            const alertCancel = document.getElementById('alert-cancel');
            const alertConfirm = document.getElementById('alert-confirm');

            // Variable to track if we're currently generating a response.
            let isGenerating = false;
            // Variable to store the controller for fetch abort.
            let abortController = null;
            // Variable to store thinking message element.
            let thinkingMessageElement = null;

            // Panel state management
            function showWelcomeScreen() {
                welcomeSection.style.display = 'block';
                suggestedActions.style.display = 'block';
                chatArea.style.display = 'none';
            }

            function showChatScreen() {
                welcomeSection.style.display = 'none';
                suggestedActions.style.display = 'none';
                chatArea.style.display = 'flex';
            }

            // Panel control functions
            function togglePanel() {
                const isCollapsed = copilotPanel.classList.contains('collapsed');
                if (isCollapsed) {
                    copilotPanel.classList.remove('collapsed');
                    copilotToggleBtn.classList.add('panel-open');
                } else {
                    copilotPanel.classList.add('collapsed');
                    copilotToggleBtn.classList.remove('panel-open');
                }
            }

            function expandPanel() {
                const isExpanded = copilotPanel.classList.contains('expanded');
                copilotPanel.classList.toggle('expanded');

                // Update expand button icon and title.
                const expandIcon = expandBtn.querySelector('i');
                if (isExpanded) {
                    // Panel is being collapsed.
                    expandIcon.className = 'fas fa-expand';
                    expandBtn.title = 'Expand';
                } else {
                    // Panel is being expanded.
                    expandIcon.className = 'fas fa-compress';
                    expandBtn.title = 'Collapse';
                }
            }

            function closePanel() {
                copilotPanel.classList.add('collapsed');
                copilotToggleBtn.classList.remove('panel-open');
            }

            // Custom alert functions.
            function showCustomAlert(title, message, confirmText = 'Confirm', cancelText = 'Cancel') {
                return new Promise((resolve) => {
                    alertTitle.textContent = title;
                    alertMessage.textContent = message;
                    alertConfirm.textContent = confirmText;
                    alertCancel.textContent = cancelText;

                    // Show the modal.
                    alertOverlay.classList.add('show');

                    // Handle confirm button.
                    const handleConfirm = () => {
                        alertOverlay.classList.remove('show');
                        alertConfirm.removeEventListener('click', handleConfirm);
                        alertCancel.removeEventListener('click', handleCancel);
                        alertOverlay.removeEventListener('click', handleOverlayClick);
                        resolve(true);
                    };

                    // Handle cancel button.
                    const handleCancel = () => {
                        alertOverlay.classList.remove('show');
                        alertConfirm.removeEventListener('click', handleConfirm);
                        alertCancel.removeEventListener('click', handleCancel);
                        alertOverlay.removeEventListener('click', handleOverlayClick);
                        resolve(false);
                    };

                    // Handle clicking outside the modal.
                    const handleOverlayClick = (e) => {
                        if (e.target === alertOverlay) {
                            handleCancel();
                        }
                    };

                    // Add event listeners.
                    alertConfirm.addEventListener('click', handleConfirm);
                    alertCancel.addEventListener('click', handleCancel);
                    alertOverlay.addEventListener('click', handleOverlayClick);
                });
            }

            // Clear chat function with custom alert.
            async function clearAndStartOver() {
                // Show custom confirmation dialog.
                const confirmed = await showCustomAlert(
                    'Clear Chat',
                    'Are you sure you want to clear the chat? This action cannot be undone.',
                    'Clear Chat',
                    'Cancel'
                );

                if (!confirmed) {
                    return; // User cancelled, do nothing.
                }

                // Clear chat and show welcome screen.
                chatMessagesContainer.innerHTML = '';
                showWelcomeScreen();

                // Clear input field and update button state.
                questionInput.value = '';
                updateSendButtonState();

                // Update auth status.
                authHelper.updateAuthStatus();

                statusElement.textContent = 'Chat cleared and restarted';
                setTimeout(() => {
                    statusElement.textContent = '';
                }, 2000);
            }

            // Function to scroll to the bottom of the chat
            function scrollToBottom() {
                requestAnimationFrame(() => {
                    chatMessagesContainer.scrollTop = chatMessagesContainer.scrollHeight;
                });
            }

            // Function to show thinking indicator in chat
            function showThinkingIndicator() {
                thinkingMessageElement = document.createElement('div');
                thinkingMessageElement.className = 'message thinking-message';

                const thinkingContent = document.createElement('div');
                thinkingContent.className = 'thinking-content';

                const typingIndicator = document.createElement('div');
                typingIndicator.className = 'typing-indicator';
                typingIndicator.innerHTML = '<span></span><span></span><span></span>';

                thinkingContent.appendChild(typingIndicator);
                thinkingMessageElement.appendChild(thinkingContent);

                chatMessagesContainer.appendChild(thinkingMessageElement);
                scrollToBottom();
            }

            // Function to hide thinking indicator
            function hideThinkingIndicator() {
                if (thinkingMessageElement) {
                    thinkingMessageElement.remove();
                    thinkingMessageElement = null;
                }
            }

            // Function to update send button state
            function updateSendButton(isGenerating) {
                const icon = submitBtn.querySelector('i');
                if (isGenerating) {
                    submitBtn.classList.add('stop-mode');
                    icon.className = 'fas fa-stop';
                    submitBtn.title = 'Stop generating';
                    submitBtn.disabled = false;
                    submitBtn.style.opacity = '1';
                    submitBtn.style.cursor = 'pointer';
                } else {
                    submitBtn.classList.remove('stop-mode');
                    icon.className = 'fas fa-arrow-up';
                    submitBtn.title = 'Send message';
                    // Update button state based on input content
                    updateSendButtonState();
                }
            }



            // Function to copy text to clipboard
            async function copyToClipboard(text) {
                try {
                    await navigator.clipboard.writeText(text);
                    return true;
                } catch (err) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        return true;
                    } catch (fallbackErr) {
                        document.body.removeChild(textArea);
                        return false;
                    }
                }
            }

            // Function removed - no longer adding copy buttons for entire messages

            // Function to add copy buttons to code blocks
            function addCodeCopyButtons(contentDiv) {
                const codeBlocks = contentDiv.querySelectorAll('pre');
                codeBlocks.forEach((pre) => {
                    const codeElement = pre.querySelector('code');
                    if (codeElement) {
                        const copyButton = document.createElement('button');
                        copyButton.className = 'code-copy-button';
                        copyButton.innerHTML = '<i class="fas fa-clone"></i><span>Copy</span>';
                        copyButton.title = 'Copy code';

                        copyButton.addEventListener('click', async (e) => {
                            e.preventDefault();
                            e.stopPropagation();

                            const codeText = codeElement.textContent;
                            const success = await copyToClipboard(codeText);
                            if (success) {
                                copyButton.innerHTML = '<i class="fas fa-check"></i><span>Copied!</span>';
                                copyButton.classList.add('copied');
                                setTimeout(() => {
                                    copyButton.innerHTML = '<i class="fas fa-clone"></i><span>Copy</span>';
                                    copyButton.classList.remove('copied');
                                }, 2000);
                            }
                        });

                        pre.appendChild(copyButton);
                    }
                });
            }

            // Function to add a message to the chat
            function addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'message-user' : 'message-assistant'}`;

                const messageHeader = document.createElement('div');
                messageHeader.className = 'message-header';

                if (!isUser) {
                    const avatarImg = document.createElement('img');
                    avatarImg.src = 'asgardeo-logo.png';
                    avatarImg.alt = 'Asgardeo';
                    messageHeader.appendChild(avatarImg);

                    const nameSpan = document.createElement('span');
                    nameSpan.textContent = 'Asgardeo Copilot';
                    messageHeader.appendChild(nameSpan);
                }

                const timeSpan = document.createElement('span');
                timeSpan.className = 'message-time';
                timeSpan.textContent = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                messageHeader.appendChild(timeSpan);

                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';

                if (isUser) {
                    contentDiv.textContent = content;
                } else {
                    contentDiv.className += ' markdown-content';
                    contentDiv.innerHTML = marked.parse(content);

                    // Apply syntax highlighting to code blocks
                    contentDiv.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightElement(block);
                    });

                    // Add copy buttons to code blocks only
                    addCodeCopyButtons(contentDiv);
                }

                messageDiv.appendChild(messageHeader);
                messageDiv.appendChild(contentDiv);

                chatMessagesContainer.appendChild(messageDiv);

                // Scroll to the bottom
                scrollToBottom();

                return contentDiv; // Return the content div for streaming updates
            }

            // Event Listeners for Panel Controls
            copilotToggleBtn.addEventListener('click', togglePanel);
            expandBtn.addEventListener('click', expandPanel);
            closeBtn.addEventListener('click', closePanel);

            // Clear chat button event listener
            clearChatBtn.addEventListener('click', (e) => {
                e.preventDefault();
                clearAndStartOver();
            });

            // Handle action card clicks
            actionCards.forEach(card => {
                card.addEventListener('click', () => {
                    const action = card.getAttribute('data-action') || card.textContent.trim();
                    questionInput.value = action;
                    updateSendButtonState();
                    showChatScreen();
                    submitBtn.click();
                });
            });



            // Keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                // Escape key to close panel
                if (e.key === 'Escape') {
                    closePanel();
                }

                // Ctrl/Cmd + Enter to send message
                if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                    if (questionInput.value.trim() && !submitBtn.disabled) {
                        submitBtn.click();
                    }
                }

                // Ctrl/Cmd + N for clear and start over
                if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                    e.preventDefault();
                    clearAndStartOver();
                }
            });

            // Set initial send button state
            updateSendButtonState();

            // Handle send/stop button click
            async function handleSubmit() {
                const question = questionInput.value.trim();

                if (!question) {
                    statusElement.textContent = 'Please enter a question';
                    setTimeout(() => {
                        statusElement.textContent = '';
                    }, 3000);
                    return;
                }

                // Show chat screen when first question is asked
                showChatScreen();

                // Add user message to chat
                addMessage(question, true);

                // Show thinking indicator and update UI
                showThinkingIndicator();
                statusElement.textContent = '';
                updateSendButton(true);

                // Set generating state
                isGenerating = true;
                window.isCurrentlyStreaming = true;

                // Create a new AbortController
                abortController = new AbortController();

                // Clear input after sending
                questionInput.value = '';
                updateSendButtonState();

                try {
                    // Get current date and time in ISO format
                    const currentDatetime = new Date().toISOString();

                    // Prepare the request payload
                    const payload = {
                        question: question,
                        version: "v1.0",
                        current_datetime: currentDatetime
                    };

                    // Get access token for authentication
                    const accessToken = authHelper.getAccessToken();
                    if (!accessToken) {
                        throw new Error('Authentication required. Please log in to Asgardeo.');
                    }

                    // Generate required headers
                    const requestId = crypto.randomUUID();
                    const correlationId = `corr-${Date.now()}`;

                    // Create headers object
                    const headers = {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`,
                        'x-request-id': requestId,
                        'correlation-id': correlationId
                    };

                    // Create a new fetch request with the appropriate headers
                    const response = await fetch('http://localhost:5001/copilot', {
                        method: 'POST',
                        headers: headers,
                        body: JSON.stringify(payload),
                        signal: abortController.signal // Add the abort signal
                    });

                    if (!response.ok) {
                        throw new Error(`Server responded with status: ${response.status}`);
                    }

                    // Get the response as a readable stream
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    let markdownContent = '';
                    let responseContentDiv = null;

                    // Process the stream
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            break;
                        }

                        // Decode the chunk
                        const chunk = decoder.decode(value, { stream: true });

                        // Process the chunk (which may contain multiple JSON objects)
                        const lines = chunk.split('\n\n');
                        for (const line of lines) {
                            if (!line.trim()) continue;

                            try {
                                const jsonObj = JSON.parse(line);

                                // Check if it's a streaming content response
                                if (jsonObj.type === "STREAM" && jsonObj.content) {
                                    // For the first chunk, hide thinking indicator and create a new message
                                    if (!responseContentDiv) {
                                        hideThinkingIndicator();
                                        responseContentDiv = addMessage(jsonObj.content, false);
                                        markdownContent = jsonObj.content;
                                    } else {
                                        // For subsequent chunks, update the existing message
                                        markdownContent += jsonObj.content;
                                        responseContentDiv.innerHTML = marked.parse(markdownContent);

                                        // Apply syntax highlighting to code blocks
                                        responseContentDiv.querySelectorAll('pre code').forEach((block) => {
                                            hljs.highlightElement(block);
                                        });

                                        // Scroll to the bottom
                                        scrollToBottom();
                                    }
                                }
                                // Handle other response types if needed
                            } catch (e) {
                                console.warn('Failed to parse JSON:', line, e);
                            }
                        }
                    }

                    // Add copy buttons to code blocks in the completed response
                    if (responseContentDiv && markdownContent) {
                        // Remove existing code copy buttons to avoid duplicates
                        responseContentDiv.querySelectorAll('.code-copy-button').forEach(btn => btn.remove());

                        // Add copy buttons to code blocks only
                        addCodeCopyButtons(responseContentDiv);
                    }

                    // Reset generating state
                    isGenerating = false;
                    window.isCurrentlyStreaming = false;
                    abortController = null;
                    updateSendButton(false);

                    // Final scroll to bottom to ensure we're at the latest message
                    scrollToBottom();
                } catch (error) {
                    console.error('Error:', error);

                    // Only show error if not aborted
                    if (error.name !== 'AbortError') {
                        statusElement.textContent = `Error: ${error.message}`;
                        // Add error message to chat
                        const errorMessage = 'An error occurred while fetching the response. Please try again.';
                        addMessage(errorMessage, false);
                    }

                    // Reset generating state
                    isGenerating = false;
                    window.isCurrentlyStreaming = false;
                    abortController = null;
                    hideThinkingIndicator();
                    updateSendButton(false);
                } finally {
                    // Update button state based on input content
                    updateSendButtonState();
                }
            }

            // Handle send/stop button click
            submitBtn.addEventListener('click', () => {
                if (isGenerating) {
                    // Stop generation
                    if (abortController) {
                        abortController.abort();
                        isGenerating = false;
                        window.isCurrentlyStreaming = false;
                        hideThinkingIndicator();
                        updateSendButton(false);
                        statusElement.textContent = '';

                        // The server will handle incomplete responses appropriately
                    }
                } else {
                    // Send message
                    handleSubmit();
                }
            });

            // Allow submitting with Enter key
            questionInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!submitBtn.disabled) {
                        submitBtn.click();
                    }
                }
            });

            // Function to update send button state based on input
            function updateSendButtonState() {
                const hasText = questionInput.value.trim().length > 0;
                if (!isGenerating) {
                    submitBtn.disabled = !hasText;
                    if (hasText) {
                        submitBtn.style.opacity = '1';
                        submitBtn.style.cursor = 'pointer';
                    } else {
                        submitBtn.style.opacity = '0.5';
                        submitBtn.style.cursor = 'not-allowed';
                    }
                }
            }

            // Auto-resize textarea as user types and update send button state
            questionInput.addEventListener('input', function() {
                this.style.height = 'auto';
                const newHeight = Math.min(this.scrollHeight, 150);
                this.style.height = newHeight + 'px';

                // Update send button state
                updateSendButtonState();
            });

            // Set up a MutationObserver to watch for changes in the chat container
            // and automatically scroll to the bottom when new content is added
            const chatObserver = new MutationObserver((mutations) => {
                // Only scroll if new messages are added, not for copy button changes
                const shouldScroll = mutations.some(mutation => {
                    // Check if new message divs are added
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        return Array.from(mutation.addedNodes).some(node =>
                            node.nodeType === Node.ELEMENT_NODE &&
                            node.classList &&
                            node.classList.contains('message')
                        );
                    }
                    // Check if message content is being updated (streaming)
                    if (mutation.type === 'characterData' ||
                        (mutation.type === 'childList' && mutation.target.classList &&
                         mutation.target.classList.contains('message-content'))) {
                        return true;
                    }
                    return false;
                });

                if (shouldScroll) {
                    scrollToBottom();
                }
            });

            // Start observing the chat messages container for changes
            chatObserver.observe(chatMessagesContainer, {
                childList: true,  // Watch for changes to child elements
                subtree: true,     // Watch the entire subtree
                characterData: true // Watch for changes to text content
            });
        });
    </script>
</body>
</html>
