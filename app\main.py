import logging
import os
from dotenv import load_dotenv
from contextlib import asynccontextmanager

import tiktoken
from fastapi import FastAPI
from langchain_openai import AzureChatOpenAI

from app.constants import (AZURE_OPENAI_SERVICE_URL, AZURE_OPENAI_API_KEY, SECRET_DIR_PATH,
                          AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_MAX_RETRIES,
                          AZURE_OPENAI_SECONDARY_API_KEY, AZURE_OPENAI_SECONDARY_SERVICE_URL,
                          AZURE_OPENAI_SECONDARY_DEPLOYMENT_NAME, AZURE_OPENAI_SECONDARY_API_VERSION,
                          AZURE_OPENAI_SECONDARY_MAX_RETRIES, TIKTOKEN_MODEL_NAME, TIKTOKEN_SECONDARY_MODEL_NAME)
from app.routes.service import router
from app.utils import get_secret
from fastapi.middleware.cors import CORSMiddleware

load_dotenv()

# Load primary Azure OpenAI service URL and key from environment variables.
azure_openai_url = os.environ.get(AZURE_OPENAI_SERVICE_URL)
azure_openai_key = get_secret(SECRET_DIR_PATH, AZURE_OPENAI_API_KEY) # os.environ.get(AZURE_OPENAI_API_KEY)

# Load secondary Azure OpenAI service URL and key from environment variables.
azure_openai_secondary_url = os.environ.get(AZURE_OPENAI_SECONDARY_SERVICE_URL)
azure_openai_secondary_key = get_secret(SECRET_DIR_PATH, AZURE_OPENAI_SECONDARY_API_KEY) # os.environ.get(AZURE_OPENAI_SECONDARY_API_KEY)

# Create a logger instance.
logger = logging.getLogger(__name__)

# Configure the logger.
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s -  %(message)s')

@asynccontextmanager
async def lifespan(app: FastAPI):
    llm = None
    encoder = None

    # Get primary configuration values from environment
    azure_openai_api_version = os.environ.get(AZURE_OPENAI_API_VERSION)
    azure_openai_deployment_name = os.environ.get(AZURE_OPENAI_DEPLOYMENT_NAME)
    azure_openai_max_retries = int(os.environ.get(AZURE_OPENAI_MAX_RETRIES))
    tiktoken_model_name = os.environ.get(TIKTOKEN_MODEL_NAME)

    # Get secondary configuration values from environment
    azure_openai_secondary_api_version = os.environ.get(AZURE_OPENAI_SECONDARY_API_VERSION)
    azure_openai_secondary_deployment_name = os.environ.get(AZURE_OPENAI_SECONDARY_DEPLOYMENT_NAME)
    azure_openai_secondary_max_retries = int(os.environ.get(AZURE_OPENAI_SECONDARY_MAX_RETRIES))
    tiktoken_secondary_model_name = os.environ.get(TIKTOKEN_SECONDARY_MODEL_NAME)

    try:
        # Initialize the primary language model.
        logger.info("Initializing the primary Azure OpenAI model.")

        llm = AzureChatOpenAI(
            azure_endpoint=azure_openai_url,
            api_key=azure_openai_key,
            deployment_name=azure_openai_deployment_name,
            api_version=azure_openai_api_version,
            max_retries=azure_openai_max_retries
        )

        # Test the primary model connection.
        try:
            _ = await llm.ainvoke("Hi, I'm Copilot.")
            logger.info(f"Successfully connected to primary Azure OpenAI model '{azure_openai_deployment_name}'.")
            encoder = tiktoken.encoding_for_model(tiktoken_model_name)
        except Exception as e:
            logger.warning(f"Failed to connect to primary model '{azure_openai_deployment_name}': {str(e)}")
            logger.info("Attempting to connect to secondary Azure OpenAI model.")

            # Try secondary model
            try:
                llm = AzureChatOpenAI(
                    azure_endpoint=azure_openai_secondary_url,
                    api_key=azure_openai_secondary_key,
                    deployment_name=azure_openai_secondary_deployment_name,
                    api_version=azure_openai_secondary_api_version,
                    max_retries=azure_openai_secondary_max_retries
                )

                _ = await llm.ainvoke("Hi, I'm Copilot.")
                logger.info(f"Successfully connected to secondary Azure OpenAI model '{azure_openai_secondary_deployment_name}'.")
                encoder = tiktoken.encoding_for_model(tiktoken_secondary_model_name)
            except Exception as secondary_e:
                logger.error(f"Failed to connect to secondary model '{azure_openai_secondary_deployment_name}': {str(secondary_e)}")
                raise Exception(f"Failed to initialize both primary and secondary Azure OpenAI models. Primary: {str(e)}, Secondary: {str(secondary_e)}")

    finally:
        if not llm or not encoder:
            # When model initialization fails.
            # Raise an exception to exit the application.
            raise Exception("Failed to initialize Azure OpenAI model and encoder")
        app.state.llm = llm
        app.state.encoder = encoder

    yield


# Create an instance of the FastAPI application.
app = FastAPI(lifespan=lifespan)

# Add CORS middleware.
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development only - restrict in production.
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the router from the service module.
app.include_router(router)
