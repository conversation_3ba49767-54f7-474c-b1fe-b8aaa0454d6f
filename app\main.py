import logging
from contextlib import asynccontextmanager

import tiktoken
from fastapi import Fast<PERSON><PERSON>
from langchain_openai import AzureChatOpenAI

from app.config import get_config, get_secret, config_loader
from app.routes.service import router
from fastapi.middleware.cors import CORSMiddleware

# Load configuration
config = get_config()

# Create a logger instance.
logger = logging.getLogger(__name__)

# Configure the logger.
logging.basicConfig(
    level=getattr(logging, config.logging.level),
    format=config.logging.format
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    llm = None
    encoder = None

    # Get configuration
    azure_config = config.azure_openai_api
    primary_deployment = config_loader.get_primary_llm_deployment()
    secondary_deployment = config_loader.get_secondary_llm_deployment()

    # Load secrets
    azure_openai_key = get_secret(azure_config.api_key_file_path)

    try:
        # Initialize the primary language model.
        logger.info("Initializing the primary Azure OpenAI model.")

        llm = AzureChatOpenAI(
            azure_endpoint=azure_config.azure_openai_endpoint,
            api_key=azure_openai_key,
            deployment_name=primary_deployment.deployment_name,
            api_version=azure_config.api_version,
            max_retries=primary_deployment.max_retries
        )

        # Test the primary model connection.
        try:
            _ = await llm.ainvoke("Hi, I'm Copilot.")
            logger.info(f"Successfully connected to primary Azure OpenAI model '{primary_deployment.deployment_name}'.")
            encoder = tiktoken.encoding_for_model(primary_deployment.model_name)
        except Exception as e:
            logger.warning(f"Failed to connect to primary model '{primary_deployment.deployment_name}': {str(e)}")
            logger.info("Attempting to connect to secondary Azure OpenAI model.")

            # Try secondary model
            try:
                llm = AzureChatOpenAI(
                    azure_endpoint=azure_config.azure_openai_endpoint,
                    api_key=azure_openai_key,
                    deployment_name=secondary_deployment.deployment_name,
                    api_version=azure_config.api_version,
                    max_retries=secondary_deployment.max_retries
                )

                _ = await llm.ainvoke("Hi, I'm Copilot.")
                logger.info(f"Successfully connected to secondary Azure OpenAI model '{secondary_deployment.deployment_name}'.")
                encoder = tiktoken.encoding_for_model(secondary_deployment.model_name)
            except Exception as secondary_e:
                logger.error(f"Failed to connect to secondary model '{secondary_deployment.deployment_name}': {str(secondary_e)}")
                raise Exception(f"Failed to initialize both primary and secondary Azure OpenAI models. Primary: {str(e)}, Secondary: {str(secondary_e)}")

    finally:
        if not llm or not encoder:
            # When model initialization fails.
            # Raise an exception to exit the application.
            raise Exception("Failed to initialize Azure OpenAI model and encoder")
        app.state.llm = llm
        app.state.encoder = encoder

    yield


# Create an instance of the FastAPI application.
app = FastAPI(lifespan=lifespan)

# Add CORS middleware.
app.add_middleware(
    CORSMiddleware,
    allow_origins=[config.cors.allowed_origins],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include the router from the service module.
app.include_router(router)
