import asyncio
import logging
import os
from typing import Optional

import httpx
from pydantic import BaseModel

from app.copilot.constants import (
    COPILOT_APP_CLIENT_ID_KEY,
    COPILOT_APP_CLIENT_SECRET_KEY,
    COPILOT_APP_TOKEN_ENDPOINT_KEY
)

logger = logging.getLogger(__name__)


class TokenInfo(BaseModel):
    access_token: str
    token_type: str
    scope: Optional[str] = None


class OAuth2TokenManager:
    """
    Manages OAuth2 client credentials flow for copilot agent communications.
    The copilot uses this token manager to authenticate with all connected agents
    that requires copilot authentication.
    Uses request retry mechanism - if a request fails with auth error, automatically
    fetches a new token and retries the request.
    """

    def __init__(self):
        self.client_id = os.environ.get(COPILOT_APP_CLIENT_ID_KEY)
        self.client_secret = os.environ.get(COPILOT_APP_CLIENT_SECRET_KEY)
        self.token_endpoint = os.environ.get(COPILOT_APP_TOKEN_ENDPOINT_KEY)
        self._token_info: Optional[TokenInfo] = None
        self._token_lock = asyncio.Lock()

        # Validate required configuration
        if not all([self.client_id, self.client_secret, self.token_endpoint]):
            missing = []
            if not self.client_id:
                missing.append(COPILOT_APP_CLIENT_ID_KEY)
            if not self.client_secret:
                missing.append(COPILOT_APP_CLIENT_SECRET_KEY)
            if not self.token_endpoint:
                missing.append(COPILOT_APP_TOKEN_ENDPOINT_KEY)

            logger.warning(f"OAuth2 configuration incomplete. Missing: {', '.join(missing)}")
            self._configured = False
        else:
            self._configured = True
            logger.info(f"OAuth2 token manager initialized successfully for endpoint: {self.token_endpoint}")

    def is_configured(self) -> bool:
        """Check if OAuth2 is properly configured."""
        return self._configured

    def _has_token(self) -> bool:
        """Check if have a token available."""
        return self._token_info is not None

    async def _fetch_new_token(self) -> TokenInfo:
        """
        Fetch a new access token using OAuth2 client credentials flow.

        Returns:
            TokenInfo: New token information

        Raises:
            httpx.HTTPError: If token request fails
            ValueError: If token response is invalid
        """
        logger.debug("Fetching new OAuth2 token")

        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }

        data = {
            "grant_type": "client_credentials",
            "client_id": self.client_id,
            "client_secret": self.client_secret
        }

        async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
            response = await client.post(
                self.token_endpoint,
                headers=headers,
                data=data
            )
            response.raise_for_status()

            token_data = response.json()

            # Validate required fields
            if "access_token" not in token_data:
                raise ValueError("Invalid token response: missing access_token")

            token_info = TokenInfo(
                access_token=token_data["access_token"],
                token_type=token_data.get("token_type", "Bearer"),
                scope=token_data.get("scope")
            )

            logger.info("Successfully obtained new OAuth2 token")
            return token_info

    async def get_access_token(self) -> Optional[str]:
        """
        Get an access token. Fetches new token if none exists.
        Token validity is checked during actual requests via retry mechanism.

        Returns:
            Optional[str]: Access token or None if OAuth2 is not configured
        """
        if not self.is_configured():
            logger.debug("OAuth2 not configured, skipping token fetch")
            return None

        async with self._token_lock:
            try:
                # Fetch token if don't have one
                if not self._has_token():
                    logger.debug("No token available, fetching new token")
                    self._token_info = await self._fetch_new_token()
                else:
                    logger.debug("Using existing token")

                return self._token_info.access_token

            except Exception as e:
                logger.error(f"Failed to obtain access token: {str(e)}", exc_info=True)
                return None

    async def refresh_token(self) -> Optional[str]:
        """
        Force refresh the token. Used by retry mechanism when current token fails.

        Returns:
            Optional[str]: New access token or None if refresh fails
        """
        if not self.is_configured():
            return None

        async with self._token_lock:
            try:
                logger.debug("Refreshing OAuth2 token due to auth failure")
                self._token_info = await self._fetch_new_token()
                return self._token_info.access_token
            except Exception as e:
                logger.error(f"Failed to refresh access token: {str(e)}", exc_info=True)
                return None

    async def get_authorization_header(self) -> Optional[str]:
        """
        Get the Authorization header value with Bearer token.

        Returns:
            Optional[str]: Authorization header value or None if token unavailable
        """
        token = await self.get_access_token()
        if token:
            return f"Bearer {token}"
        return None

# Global token manager instance
_token_manager: Optional[OAuth2TokenManager] = None


def get_token_manager() -> OAuth2TokenManager:
    """Get the global token manager instance."""
    global _token_manager
    if _token_manager is None:
        _token_manager = OAuth2TokenManager()
    return _token_manager
