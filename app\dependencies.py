from fastapi import Depends, HTTPException
from fastapi import Request, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from app.auth import introspect_opaque_token
from app.model import AuthContext

logger = logging.getLogger(__name__)


async def authenticate(request: Request,
                       credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False))) -> AuthContext:
    """
    Authenticate user using opaque token introspection.

    Args:
        request: FastAPI request object
        credentials: HTTP Bearer token credentials

    Returns:
        AuthContext: Authenticated user context

    Raises:
        HTTPException: If authentication fails
    """

    # Check if credentials are provided.
    if not credentials or not credentials.credentials:
        logger.warning("No authorization credentials provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header with Bearer token is required",
            headers={"WWW-Authenticate": "Bearer"}
        )

    try:
        # Use opaque token introspection for authentication.
        auth_context = await introspect_opaque_token(credentials.credentials)
        logger.info(f"Successfully authenticated user: {auth_context.sub}")
        return auth_context

    except ValueError as e:
        # Token validation failed.
        logger.warning(f"Token validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"}
        )
    except Exception as e:
        # Unexpected error during authentication.
        logger.error(f"Unexpected error during authentication: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"}
        ) from e
