from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from app.auth import introspect_opaque_token
from app.constants import BEARER_SCHEME, WWW_AUTHENTICATE_HEADER
from app.exceptions import TokenValidationError, TokenIntrospectionError
from app.model import AuthContext

logger = logging.getLogger(__name__)


async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer(auto_error=False))) -> AuthContext:
    """
    Authenticate user using opaque token introspection.

    Args:
        credentials: HTTP Bearer token credentials

    Returns:
        AuthContext: Authenticated user context

    Raises:
        HTTPException: If authentication fails (401 for auth errors, 503 for service errors)
    """

    # Check if credentials are provided.
    if not credentials or not credentials.credentials:
        logger.warning("No authorization credentials provided")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization header with Bearer token is required",
            headers={WWW_AUTHENTICATE_HEADER: BEARER_SCHEME}
        )

    try:
        # Use opaque token introspection for authentication.
        auth_context = await introspect_opaque_token(credentials.credentials)
        logger.info(f"Successfully authenticated user: {auth_context.sub}")
        return auth_context

    except TokenValidationError as e:
        # Token validation failed (invalid token, expired, etc.).
        logger.warning(f"Token validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token validation failed: {str(e)}",
            headers={WWW_AUTHENTICATE_HEADER: BEARER_SCHEME}
        )
    except TokenIntrospectionError as e:
        # Authentication service error (network, server error, etc.).
        logger.error(f"Authentication service error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service temporarily unavailable"
        )
    except Exception as e:
        # Unexpected error during authentication.
        logger.error(f"Unexpected error during authentication: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal authentication error"
        ) from e
