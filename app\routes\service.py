import logging
import os
import re
from distutils.util import strtobool
from enum import Enum
from typing import Optional, Dict

from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, constr
from sse_starlette.sse import EventSourceResponse

from app.cache import fetch_cache, update_cache, ping
from app.constants import X_REQUEST_ID, CORRELATION_ID, DOCS_ASSISTANT_ENABLED
from app.copilot.agent import Agent
from app.copilot.exception import AgentException
from app.copilot.tools import ToolContext, AsgardeoDocsAssistant
from app.dependencies import authenticate
from app.model import AuthContext, CacheRecord, HistoryRecord

router = APIRouter()


class QuestionInput(BaseModel):
    question: constr(min_length=1, max_length=256)
    version: Optional[str] = "v1.0"
    current_datetime: str


class ResponseType(str, Enum):
    JSON = "JSON",
    STREAM = "STREAM",
    ERROR = "ERROR"


class Response(BaseModel):
    type: ResponseType


class ErrorResponse(Response):
    message: str
    tracking_id: str
    type: ResponseType = ResponseType.ERROR


class TextResponse(Response):
    content: str
    type: ResponseType = ResponseType.STREAM


class JSONResponse(Response):
    type: ResponseType = ResponseType.JSON
    assistant: str
    call_id: str
    result: Dict


# Assistant switching flags.
docs_assistant_switch = strtobool(os.environ.get(DOCS_ASSISTANT_ENABLED, "True"))

# Create the tools list.
tools = []
if docs_assistant_switch:
    tools.append(AsgardeoDocsAssistant)

# Pattern for question validation.
pattern = re.compile(r'[a-zA-Z0-9]+')


@router.post("/copilot")
async def stream(request: Request, body: QuestionInput, auth_context: AuthContext = Depends(authenticate)):
    request_id = request.headers.get(X_REQUEST_ID)
    correlation_id = request.headers.get(CORRELATION_ID)
    user_id = auth_context.sub

    if not request_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing '{X_REQUEST_ID}' header.")

    if not correlation_id:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Missing '{CORRELATION_ID}' header.")

    async def execution_event_generator():
        question = body.question
        if not pattern.search(question):  # Validate the question.
            yield TextResponse(
                content="Sorry, that's not a valid question. Your question needs to include letters or numbers."
            ).model_dump_json()
            return

        # Retrieve cached history.
        try:
            history = await fetch_cache(session_id=user_id)
        except Exception:
            logging.error(f"{user_id} - Error while retrieving history", exc_info=True)
            yield ErrorResponse(
                message="Copilot is unable to handle your request at the moment.",
                tracking_id=user_id).model_dump_json()
            return
        logging.debug(f"For user '{user_id}' restored history: {history}.")

        llm = request.app.state.llm
        agent = Agent(llm, tools, request.app.state.encoder)
        context = ToolContext(auth_context=auth_context,
                              session_id=user_id,
                              history=history,
                              original_question=question,
                              current_datetime=body.current_datetime,
                              llm=llm)

        try:
            async for event in agent.execute(question, context):
                if isinstance(event, str):
                    text_response = TextResponse(content=event).model_dump_json()
                    yield text_response
                elif isinstance(event, BaseModel):
                    tool_selection_response = event.model_dump_json()
                    yield tool_selection_response
                else:
                    raise ValueError(f"Invalid event type returned by the agent: {type(event)}")
                if body.version != "v2.0":
                    yield "\n\n"
        except AgentException as e:
            logging.error(f"{user_id} - Error occurred while executing copilot", exc_info=True)
            yield ErrorResponse(message=str(e), tracking_id=user_id).model_dump_json()
            return
        except Exception:
            logging.error(f"{user_id} - Unexpected error occurred while executing copilot", exc_info=True)
            yield ErrorResponse(message="Copilot is unable to handle your request at the moment.",
                                tracking_id=user_id).model_dump_json()
            return

        history.history.append(HistoryRecord(question=question, answer=agent.answer))
        cached = CacheRecord(history=history.history, summary=agent.summary)
        await update_cache(user_id, cached)

    version = body.version
    if version.lower() == "v2.0":
        return EventSourceResponse(execution_event_generator(), media_type="text/event-stream")
    else:
        response = StreamingResponse(execution_event_generator(), media_type="text/event-stream")
        response.headers["X-Accel-Buffering"] = "no"
        response.headers["cache-control"] = "no-cache"
        return response


@router.get("/health")
async def health_check():
    if await ping():
            return {"status": "UP"}
    raise HTTPException(status_code=500, detail="Cache is down.")