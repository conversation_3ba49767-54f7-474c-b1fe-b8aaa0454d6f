#  Copyright (c) 2024, WSO2 LLC. (http://www.wso2.com).
#
#  This software is the property of WSO2 LLC. and its suppliers, if any.
#  Dissemination of any information or reproduction of any material contained
#  herein in any form is strictly forbidden, unless permitted by WSO2 expressly.
#  You may not alter or remove any copyright or other notice from copies of this content.

azureOpenAiApi:
  apiType: "azure"
  apiVersion: "2025-01-01-preview"
  azureOpenAiEndpoint: "https://is-ai-testing-eastus2.openai.azure.com/"
  apiKeyFilePath: "configs/azureOpenAIConfigs/apiKey/azureAPIKey"

  llmDeployments:
    - name: "primaryModel"
      deploymentName: "gpt-4o-2024-11-20"
      modelName: "gpt-4o"
      modelVersion: "2024-11-20"
      temperature: 0.7
      maxRetries: 0

    - name: "secondaryModel"
      deploymentName: "gpt-4o-2024-11-20"
      modelName: "gpt-4o"
      modelVersion: "2024-11-20"
      temperature: 0.7
      maxRetries: 0

userQuery:
  wordLimit: 150
  maxQuestionLength: 256

tokenValidation:
  enabled: true
  introspectionEndpoint: "https://api.asgardeo.io/t/thakshaka/oauth2/introspect"
  clientId: "JqVDqN2x9Bczdtw4U7dXfcm21bga"
  clientSecretFilePath: "configs/asgardeoApplication/clientSecret"
  hostNameVerification: false
  tokenIntrospectionTimeout: 2

copilotOAuth2:
  clientId: "Xzvf4QchjYTNgZ6X2QKSzuxqaXUa"
  clientSecretFilePath: "configs/copilotApplication/clientSecret"
  tokenEndpoint: "https://api.asgardeo.io/t/thakshaka/oauth2/token"

tools:
  docsAssistant:
    enabled: true
    url: "https://d83e807a-3e9b-40d8-8f00-828b095f98f9-dev.e1-us-east-azure.choreoapis.dev/asgardeo-co-pilot/docs-assistant/v1/docs-assistant"

cosmosDb:
  connectionStringFilePath: "configs/cosmosDbConfigs/connectionString"
  databaseName: "asgardeo-ai-copilot-cosmos-eastus2"
  containerName: "conversation-history"
  conversationCacheTtl: 3600
  maxHistoryRecords: 10

cors:
  allowedOrigins: "*"

logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

retry:
  maxAttempts: 2

constants:
  copilotNamespace: "copilot"
  secretDirPath: "/mnt/csi/secret-ai-copilot"
  correlationIdHeaderName: "correlation-id"
  requestIdHeaderName: "x-request-id"
