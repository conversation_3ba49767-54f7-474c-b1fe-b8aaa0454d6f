# File-Based Configuration Guide

This document explains the file-based configuration approach for sensitive values like client IDs and organization names in the Asgardeo AI Copilot application.

## Overview

All sensitive configuration values are now stored in separate files within the `configs/` directory, rather than directly in the YAML configuration file. This provides better security, easier environment management, and cleaner separation of concerns.

## Configuration Structure

### YAML Configuration (config.yaml)
The YAML file now contains only file paths, not actual values:

```yaml
tokenValidation:
  enabled: true
  organizationFilePath: "configs/asgardeoApplication/organization"
  clientIdFilePath: "configs/asgardeoApplication/clientId"
  clientSecretFilePath: "configs/asgardeoApplication/clientSecret"
  hostNameVerification: false
  tokenIntrospectionTimeout: 2

copilotOAuth2:
  organizationFilePath: "configs/copilotApplication/organization"
  clientIdFilePath: "configs/copilotApplication/clientId"
  clientSecretFilePath: "configs/copilotApplication/clientSecret"
```

### File Structure
```
configs/
├── asgardeoApplication/
│   ├── organization          # Asgardeo organization name
│   ├── clientId              # Token introspection client ID
│   └── clientSecret          # Token introspection client secret
├── copilotApplication/
│   ├── organization          # Asgardeo organization name
│   ├── clientId              # Copilot OAuth2 client ID
│   └── clientSecret          # Copilot OAuth2 client secret
├── azureOpenAIConfigs/
│   └── apiKey/
│       └── azureAPIKey       # Azure OpenAI API key
└── cosmosDbConfigs/
    └── connectionString      # Cosmos DB connection string
```

## File Contents

### Organization Files
```
# configs/asgardeoApplication/organization
# configs/copilotApplication/organization
thakshaka
```

### Client ID Files
```
# configs/asgardeoApplication/clientId
JqVDqN2x9Bczdtw4U7dXfcm21bga

# configs/copilotApplication/clientId
Xzvf4QchjYTNgZ6X2QKSzuxqaXUa
```

### Client Secret Files
```
# configs/asgardeoApplication/clientSecret
LZe9SJpqtZkWinRDDam0pjf4hQnTDYVEcX95CAN7VM4a

# configs/copilotApplication/clientSecret
5lf9xKLozAtBrImKSlsWInNm929lY05GtChpw14ewj0a
```

## Implementation Details

### Configuration Data Classes
```python
@dataclass
class TokenValidationConfig:
    enabled: bool
    organization_file_path: str      # Path to organization file
    client_id_file_path: str         # Path to client ID file
    client_secret_file_path: str     # Path to client secret file
    hostname_verification: bool
    token_introspection_timeout: int

@dataclass
class CopilotOAuth2Config:
    organization_file_path: str      # Path to organization file
    client_id_file_path: str         # Path to client ID file
    client_secret_file_path: str     # Path to client secret file
```

### Runtime Value Loading
```python
# auth.py - Token introspection
organization = get_secret(config.token_validation.organization_file_path)
client_id = get_secret(config.token_validation.client_id_file_path)
client_secret = get_secret(config.token_validation.client_secret_file_path)
introspection_endpoint = f"https://api.asgardeo.io/t/{organization}/oauth2/introspect"

# token_manager.py - OAuth2 token
self.organization = get_secret(config.copilot_oauth2.organization_file_path)
self.client_id = get_secret(config.copilot_oauth2.client_id_file_path)
self.client_secret = get_secret(config.copilot_oauth2.client_secret_file_path)
self.token_endpoint = f"https://api.asgardeo.io/t/{self.organization}/oauth2/token"
```

## Benefits

### ✅ **Enhanced Security**
- No sensitive values in YAML configuration
- Each value in separate file with appropriate permissions
- Easy to apply different security policies per file type

### ✅ **Environment Management**
- Easy to switch between environments by changing file contents
- No need to modify YAML configuration for different environments
- Clear separation between configuration structure and values

### ✅ **Version Control Safety**
- YAML configuration can be safely committed
- Sensitive files protected by .gitignore
- Template files provide structure without exposing secrets

### ✅ **Operational Flexibility**
- Values can be mounted from different sources (secrets, config maps, etc.)
- Easy to automate deployment with different file sources
- Support for container orchestration secret management

### ✅ **Consistency**
- All sensitive values follow same file-based pattern
- Uniform approach across different types of credentials
- Predictable file structure and naming

## Environment-Specific Configuration

### Development Environment
```bash
# configs/asgardeoApplication/organization
dev-org

# configs/copilotApplication/organization  
dev-org

# configs/asgardeoApplication/clientId
dev-client-id-123

# configs/copilotApplication/clientId
dev-copilot-client-456
```

### Production Environment
```bash
# configs/asgardeoApplication/organization
prod-company

# configs/copilotApplication/organization
prod-company

# configs/asgardeoApplication/clientId
prod-client-id-789

# configs/copilotApplication/clientId
prod-copilot-client-012
```

## Security Considerations

### File Permissions
```bash
# Recommended file permissions
chmod 600 configs/*/clientSecret    # Read-write for owner only
chmod 644 configs/*/organization    # Read for owner/group
chmod 644 configs/*/clientId        # Read for owner/group
```

### .gitignore Protection
```gitignore
# All config files are protected
configs/
**/clientId
**/organization
**/clientSecret
**/apiKey
**/connectionString
```

## Migration from Previous Approach

### Before: Values in YAML
```yaml
tokenValidation:
  organization: "thakshaka"
  clientId: "JqVDqN2x9Bczdtw4U7dXfcm21bga"
  
copilotOAuth2:
  organization: "thakshaka"
  clientId: "Xzvf4QchjYTNgZ6X2QKSzuxqaXUa"
```

### After: File Paths in YAML
```yaml
tokenValidation:
  organizationFilePath: "configs/asgardeoApplication/organization"
  clientIdFilePath: "configs/asgardeoApplication/clientId"
  
copilotOAuth2:
  organizationFilePath: "configs/copilotApplication/organization"
  clientIdFilePath: "configs/copilotApplication/clientId"
```

## Template Structure

### config.template.yaml
```yaml
tokenValidation:
  organizationFilePath: "configs/asgardeoApplication/organization"
  clientIdFilePath: "configs/asgardeoApplication/clientId"
  clientSecretFilePath: "configs/asgardeoApplication/clientSecret"

copilotOAuth2:
  organizationFilePath: "configs/copilotApplication/organization"
  clientIdFilePath: "configs/copilotApplication/clientId"
  clientSecretFilePath: "configs/copilotApplication/clientSecret"
```

### configs.template/ Directory
```
configs.template/
├── asgardeoApplication/
│   ├── organization.template          # "your-asgardeo-organization-name"
│   ├── clientId.template              # "your-introspection-client-id"
│   └── clientSecret.template          # "your-introspection-client-secret"
├── copilotApplication/
│   ├── organization.template          # "your-asgardeo-organization-name"
│   ├── clientId.template              # "your-copilot-client-id"
│   └── clientSecret.template          # "your-copilot-client-secret"
├── azureOpenAIConfigs/
│   └── apiKey/
│       └── azureAPIKey.template       # "your-azure-openai-api-key"
└── cosmosDbConfigs/
    └── connectionString.template      # "AccountEndpoint=...;AccountKey=...;"
```

## Best Practices

1. **File Naming**: Use descriptive names without extensions
2. **Content Format**: Single line, no trailing whitespace
3. **Permissions**: Restrict access to sensitive files
4. **Validation**: Verify file existence and content at startup
5. **Documentation**: Keep template files updated with examples
6. **Backup**: Include config files in backup strategies
7. **Monitoring**: Monitor for missing or corrupted config files

This file-based approach provides a secure, flexible, and maintainable way to manage sensitive configuration values while keeping the YAML configuration clean and version-control friendly.
