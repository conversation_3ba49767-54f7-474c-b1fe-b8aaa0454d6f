"""
Custom exceptions for authentication and authorization.
"""


class AuthenticationError(Exception):
    """Raised when authentication fails due to invalid credentials."""
    pass


class TokenValidationError(AuthenticationError):
    """Raised when token validation fails (invalid, expired, etc.)."""
    pass


class AuthorizationError(Exception):
    """Raised when user is authenticated but not authorized for the resource."""
    pass


class AuthServiceError(Exception):
    """Raised when authentication service is unavailable or returns an error."""
    pass


class TokenIntrospectionError(AuthServiceError):
    """Raised when token introspection service fails."""
    pass
