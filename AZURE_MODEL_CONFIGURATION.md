# Azure OpenAI Model Configuration Guide

This document explains how to configure the Asgardeo AI Copilot to work with any Azure OpenAI model without changing the code.

## Configuration Variables

All Azure OpenAI model configurations are controlled through environment variables. You can set these in your `.env` file, environment variables, or container configuration.

### Primary Model Configuration

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `AZURE_OPENAI_API_VERSION` | Azure OpenAI API version | `2025-01-01-preview` | `2024-02-15-preview` |
| `AZURE_OPENAI_DEPLOYMENT_NAME` | Primary model deployment name | `gpt-4o-2024-11-20` | `gpt-35-turbo` |
| `AZURE_OPENAI_MAX_RETRIES` | Maximum retry attempts for API calls | `0` | `3` |

### Fallback Model Configuration

If the primary model fails to connect, the system will automatically try the fallback model:

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `AZURE_OPENAI_FALLBACK_DEPLOYMENT_NAME` | Fallback model deployment name | `gpt-4o-2024-11-20` | `gpt-35-turbo-16k` |
| `AZURE_OPENAI_FALLBACK_MODEL_NAME` | Fallback model name for encoding | `gpt-4o` | `gpt-3.5-turbo` |

### Tiktoken Configuration

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `TIKTOKEN_MODEL_NAME` | Model name for token encoding | `gpt-4o` | `gpt-3.5-turbo` |

### Other Configuration

| Variable | Description | Default Value | Example |
|----------|-------------|---------------|---------|
| `MAX_QUESTION_LENGTH` | Maximum question length in characters | `256` | `512` |

## Example Configurations

### GPT-4o Configuration (Default)
```env
AZURE_OPENAI_API_VERSION=2025-01-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o-2024-11-20
AZURE_OPENAI_MAX_RETRIES=0
AZURE_OPENAI_FALLBACK_DEPLOYMENT_NAME=gpt-4o-2024-11-20
AZURE_OPENAI_FALLBACK_MODEL_NAME=gpt-4o
TIKTOKEN_MODEL_NAME=gpt-4o
```

### GPT-3.5 Turbo Configuration
```env
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-35-turbo
AZURE_OPENAI_MAX_RETRIES=2
AZURE_OPENAI_FALLBACK_DEPLOYMENT_NAME=gpt-35-turbo-16k
AZURE_OPENAI_FALLBACK_MODEL_NAME=gpt-3.5-turbo
TIKTOKEN_MODEL_NAME=gpt-3.5-turbo
```

### GPT-4 Configuration
```env
AZURE_OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
AZURE_OPENAI_MAX_RETRIES=1
AZURE_OPENAI_FALLBACK_DEPLOYMENT_NAME=gpt-4-32k
AZURE_OPENAI_FALLBACK_MODEL_NAME=gpt-4
TIKTOKEN_MODEL_NAME=gpt-4
```

## How It Works

1. **Primary Model**: The application first tries to connect to the model specified in `AZURE_OPENAI_DEPLOYMENT_NAME`
2. **Fallback Model**: If the primary model fails, it automatically switches to `AZURE_OPENAI_FALLBACK_DEPLOYMENT_NAME`
3. **Token Encoding**: Uses `TIKTOKEN_MODEL_NAME` for calculating token counts and managing conversation history
4. **Retry Logic**: Retries failed API calls up to `AZURE_OPENAI_MAX_RETRIES` times

## Deployment Examples

### Docker
```bash
docker run -e AZURE_OPENAI_DEPLOYMENT_NAME=gpt-35-turbo \
           -e TIKTOKEN_MODEL_NAME=gpt-3.5-turbo \
           your-copilot-image
```

### Kubernetes
```yaml
env:
  - name: AZURE_OPENAI_DEPLOYMENT_NAME
    value: "gpt-35-turbo"
  - name: TIKTOKEN_MODEL_NAME
    value: "gpt-3.5-turbo"
```

### Environment Variables
```bash
export AZURE_OPENAI_DEPLOYMENT_NAME=gpt-35-turbo
export TIKTOKEN_MODEL_NAME=gpt-3.5-turbo
```

## Important Notes

- **Model Names**: Ensure the `TIKTOKEN_MODEL_NAME` matches the actual model family (e.g., `gpt-4`, `gpt-3.5-turbo`)
- **API Versions**: Use the appropriate API version for your Azure OpenAI service
- **Deployment Names**: Use the exact deployment names from your Azure OpenAI resource
- **Token Limits**: Different models have different token limits; adjust `MAX_QUESTION_LENGTH` accordingly
- **Connection Testing**: The application tests the model connection at startup and will fail if unable to connect

## Troubleshooting

### Model Configuration Issues
- **Connection Issues**: Check your deployment names and API version
- **Token Encoding Errors**: Ensure `TIKTOKEN_MODEL_NAME` matches your model family
- **Rate Limiting**: Increase `AZURE_OPENAI_MAX_RETRIES` for better resilience
- **Long Questions**: Adjust `MAX_QUESTION_LENGTH` based on your model's capabilities
- **Startup Failures**: Check logs for specific connection error messages

### Authentication Issues
- **401 Unauthorized**: Invalid or expired authentication tokens
- **503 Service Unavailable**: Authentication service is temporarily down
- **500 Internal Server Error**: Unexpected authentication errors
