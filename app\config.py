"""
Loads configuration from config.yaml and provides typed access to configuration values.
"""

import yaml
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class LLMDeployment:
    """Configuration for an LLM deployment."""
    name: str
    deployment_name: str
    model_name: str
    model_version: str
    temperature: float
    max_retries: int


@dataclass
class AzureOpenAIConfig:
    """Azure OpenAI API configuration."""
    api_type: str
    api_version: str
    azure_openai_endpoint: str
    api_key_file_path: str
    llm_deployments: List[LLMDeployment]


@dataclass
class UserQueryConfig:
    """User query configuration."""
    word_limit: int
    max_question_length: int


@dataclass
class TokenValidationConfig:
    """Token validation configuration."""
    enabled: bool
    organization: str
    client_id: str
    client_secret_file_path: str
    hostname_verification: bool
    token_introspection_timeout: int


@dataclass
class CopilotOAuth2Config:
    """Copilot OAuth2 configuration."""
    organization: str
    client_id: str
    client_secret_file_path: str


@dataclass
class DocsAssistantConfig:
    """Docs assistant tool configuration."""
    enabled: bool
    url: str


@dataclass
class ToolsConfig:
    """Tools configuration."""
    docs_assistant: DocsAssistantConfig


@dataclass
class CosmosDbConfig:
    """Cosmos DB configuration."""
    connection_string_file_path: str
    database_name: str
    container_name: str
    conversation_cache_ttl: int
    max_history_records: int


@dataclass
class CorsConfig:
    """CORS configuration."""
    allowed_origins: str


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str
    format: str


@dataclass
class RetryConfig:
    """Retry configuration."""
    max_attempts: int


@dataclass
class ConstantsConfig:
    """Application constants."""
    copilot_namespace: str
    secret_dir_path: str
    correlation_id_header_name: str
    request_id_header_name: str


@dataclass
class AppConfig:
    """Main application configuration."""
    azure_openai_api: AzureOpenAIConfig
    user_query: UserQueryConfig
    token_validation: TokenValidationConfig
    copilot_oauth2: CopilotOAuth2Config
    tools: ToolsConfig
    cosmos_db: CosmosDbConfig
    cors: CorsConfig
    logging: LoggingConfig
    retry: RetryConfig
    constants: ConstantsConfig


class ConfigLoader:
    """Configuration loader for the application."""

    def __init__(self, config_path: str = "config.yaml"):
        """Initialize the configuration loader.

        Args:
            config_path: Path to the configuration YAML file
        """
        self.config_path = config_path
        self._config: Optional[AppConfig] = None

    def load_config(self) -> AppConfig:
        """Load configuration from YAML file.

        Returns:
            AppConfig: Loaded configuration

        Raises:
            FileNotFoundError: If config file is not found
            yaml.YAMLError: If YAML parsing fails
            ValueError: If configuration is invalid
        """
        if self._config is not None:
            return self._config

        if not Path(self.config_path).exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")

        with open(self.config_path, 'r') as file:
            config_data = yaml.safe_load(file)

        self._config = self._parse_config(config_data)
        return self._config

    def _parse_config(self, config_data: Dict[str, Any]) -> AppConfig:
        """Parse configuration data into typed configuration objects.

        Args:
            config_data: Raw configuration data from YAML

        Returns:
            AppConfig: Parsed configuration
        """
        # Parse Azure OpenAI configuration
        azure_config_data = config_data["azureOpenAiApi"]
        llm_deployments = [
            LLMDeployment(
                name=deployment["name"],
                deployment_name=deployment["deploymentName"],
                model_name=deployment["modelName"],
                model_version=deployment["modelVersion"],
                temperature=deployment["temperature"],
                max_retries=deployment.get("maxRetries", 0)
            )
            for deployment in azure_config_data["llmDeployments"]
        ]

        azure_openai_config = AzureOpenAIConfig(
            api_type=azure_config_data["apiType"],
            api_version=azure_config_data["apiVersion"],
            azure_openai_endpoint=azure_config_data["azureOpenAiEndpoint"],
            api_key_file_path=azure_config_data["apiKeyFilePath"],
            llm_deployments=llm_deployments
        )

        # Parse other configurations
        user_query_config = UserQueryConfig(
            word_limit=config_data["userQuery"]["wordLimit"],
            max_question_length=config_data["userQuery"]["maxQuestionLength"]
        )

        token_validation_config = TokenValidationConfig(
            enabled=config_data["tokenValidation"]["enabled"],
            organization=config_data["tokenValidation"]["organization"],
            client_id=config_data["tokenValidation"]["clientId"],
            client_secret_file_path=config_data["tokenValidation"]["clientSecretFilePath"],
            hostname_verification=config_data["tokenValidation"]["hostNameVerification"],
            token_introspection_timeout=config_data["tokenValidation"]["tokenIntrospectionTimeout"]
        )

        copilot_oauth2_config = CopilotOAuth2Config(
            organization=config_data["copilotOAuth2"]["organization"],
            client_id=config_data["copilotOAuth2"]["clientId"],
            client_secret_file_path=config_data["copilotOAuth2"]["clientSecretFilePath"]
        )

        docs_assistant_config = DocsAssistantConfig(
            enabled=config_data["tools"]["docsAssistant"]["enabled"],
            url=config_data["tools"]["docsAssistant"]["url"]
        )

        tools_config = ToolsConfig(docs_assistant=docs_assistant_config)

        cosmos_db_config = CosmosDbConfig(
            connection_string_file_path=config_data["cosmosDb"]["connectionStringFilePath"],
            database_name=config_data["cosmosDb"]["databaseName"],
            container_name=config_data["cosmosDb"]["containerName"],
            conversation_cache_ttl=config_data["cosmosDb"]["conversationCacheTtl"],
            max_history_records=config_data["cosmosDb"]["maxHistoryRecords"]
        )

        cors_config = CorsConfig(
            allowed_origins=config_data["cors"]["allowedOrigins"]
        )

        logging_config = LoggingConfig(
            level=config_data["logging"]["level"],
            format=config_data["logging"]["format"]
        )

        retry_config = RetryConfig(
            max_attempts=config_data["retry"]["maxAttempts"]
        )

        constants_config = ConstantsConfig(
            copilot_namespace=config_data["constants"]["copilotNamespace"],
            secret_dir_path=config_data["constants"]["secretDirPath"],
            correlation_id_header_name=config_data["constants"]["correlationIdHeaderName"],
            request_id_header_name=config_data["constants"]["requestIdHeaderName"]
        )

        return AppConfig(
            azure_openai_api=azure_openai_config,
            user_query=user_query_config,
            token_validation=token_validation_config,
            copilot_oauth2=copilot_oauth2_config,
            tools=tools_config,
            cosmos_db=cosmos_db_config,
            cors=cors_config,
            logging=logging_config,
            retry=retry_config,
            constants=constants_config
        )

    def get_primary_llm_deployment(self) -> LLMDeployment:
        """Get the primary LLM deployment configuration.

        Returns:
            LLMDeployment: Primary LLM deployment configuration
        """
        config = self.load_config()
        for deployment in config.azure_openai_api.llm_deployments:
            if deployment.name == "primaryModel":
                return deployment
        raise ValueError("Primary model deployment not found in configuration")

    def get_secondary_llm_deployment(self) -> LLMDeployment:
        """Get the secondary LLM deployment configuration.

        Returns:
            LLMDeployment: Secondary LLM deployment configuration
        """
        config = self.load_config()
        for deployment in config.azure_openai_api.llm_deployments:
            if deployment.name == "secondaryModel":
                return deployment
        raise ValueError("Secondary model deployment not found in configuration")


# Global configuration loader instance
config_loader = ConfigLoader()


def get_config() -> AppConfig:
    """Get the application configuration.

    Returns:
        AppConfig: Application configuration
    """
    return config_loader.load_config()


def get_secret(file_path: str) -> str:
    """Get secret value from file path.

    Args:
        file_path: Path to the secret file

    Returns:
        str: Secret value

    Raises:
        FileNotFoundError: If secret file is not found
        ValueError: If secret value is empty
    """
    if not Path(file_path).exists():
        raise FileNotFoundError(f"Secret file not found: {file_path}")

    with open(file_path, 'r') as f:
        secret = f.read().strip()

    if not secret:
        raise ValueError(f"Secret value in file '{file_path}' is empty")

    return secret
