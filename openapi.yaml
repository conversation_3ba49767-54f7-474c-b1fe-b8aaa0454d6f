openapi: 3.1.0
info:
  title: Asgardeo AI Copilot API
  description: API for testing the Asgardeo AI Copilot endpoints
  version: 1.0.0

servers:
  - url: http://localhost:5001
    description: Local development server

security:
  - bearerAuth: []

paths:
  /copilot/chat:
    post:
      summary: Interact with the AI Copilot
      operationId: interactWithCopilot
      parameters:
        - name: x-request-id
          in: header
          required: true
          schema:
            type: string
            format: uuid
        - name: correlation-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QuestionInput'
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/TextResponse'
                  - $ref: '#/components/schemas/JSONResponse'
                  - $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal server error

  /health:
    get:
      summary: Health check endpoint
      operationId: healthCheck
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
        '500':
          description: Service is unhealthy

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    QuestionInput:
      type: object
      required:
        - question
        - current_datetime
      properties:
        question:
          type: string
          minLength: 1
          maxLength: 1024
          description: "Question text (max length configurable via MAX_QUESTION_LENGTH environment variable, default: 256)"
        version:
          type: string
          default: "v1.0"
          enum:
            - "v1.0"
            - "v2.0"
        current_datetime:
          type: string

    ResponseType:
      type: string
      enum:
        - JSON
        - STREAM
        - ERROR

    Response:
      type: object
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/ResponseType'

    TextResponse:
      allOf:
        - $ref: '#/components/schemas/Response'
        - type: object
          required:
            - content
          properties:
            content:
              type: string
            type:
              type: string
              enum:
                - STREAM
              default: STREAM

    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/Response'
        - type: object
          required:
            - message
            - tracking_id
          properties:
            message:
              type: string
            tracking_id:
              type: string
            type:
              type: string
              enum:
                - ERROR
              default: ERROR

    JSONResponse:
      allOf:
        - $ref: '#/components/schemas/Response'
        - type: object
          required:
            - assistant
            - call_id
            - result
          properties:
            assistant:
              type: string
            call_id:
              type: string
            result:
              type: object
              additionalProperties: true
            type:
              type: string
              enum:
                - JSON
              default: JSON
