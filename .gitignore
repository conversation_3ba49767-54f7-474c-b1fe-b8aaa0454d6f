# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Configuration and Secrets
configs/
config.yaml
config.*.yaml
*.key
*.secret
secrets/
secret-*

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.classpath
.settings/

# Jupyter Notebook
.ipynb_checkpoints

# Azure Cosmos DB
local-cosmos-db/

# Redis
dump.rdb

# Logs
logs/
*.log.*

# Security and Sensitive Files
**/apiKey
**/clientSecret
**/clientId
**/organization
**/connectionString
**/*Secret*
**/*Key*
**/*Token*
**/*Password*
**/*ClientId*
**/*Organization*
**/credentials*
**/auth*
.secrets
.credentials

# Azure and Cloud Credentials
azure-credentials.json
service-account.json
gcp-credentials.json
aws-credentials.json

# SSL/TLS Certificates
*.pem
*.crt
*.cer
*.p12
*.pfx
*.jks

# Database Files
*.db
*.sqlite
*.sqlite3

# Temporary and Test Files
temp/
tmp/
test-data/
*.tmp
*.temp
.cache/
