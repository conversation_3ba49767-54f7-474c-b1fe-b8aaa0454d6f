import os
import re
from dotenv import load_dotenv
import logging

import httpx

from app.constants import (
    ASGARDEO_INTROSPECTION_ENDPOINT, ASGARDEO_INTROSPECTION_CLIENT_ID,
    ASGARDEO_INTROSPECTION_CLIENT_SECRET
)
from app.model import AuthContext, Organization

load_dotenv()

logger = logging.getLogger(__name__)


async def introspect_opaque_token(auth_token: str) -> AuthContext:
    """
    Introspect an opaque token using Asgardeo's introspection endpoint.

    Args:
        auth_token: The opaque access token to validate

    Returns:
        AuthContext: Authentication context with user information

    Raises:
        ValueError: If token is invalid or introspection fails
        httpx.HTTPError: If HTTP request fails
    """
    introspection_endpoint = os.environ.get(ASGARDEO_INTROSPECTION_ENDPOINT)
    client_id = os.environ.get(ASGARDEO_INTROSPECTION_CLIENT_ID)
    client_secret = os.environ.get(ASGARDEO_INTROSPECTION_CLIENT_SECRET)

    async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
        try:
            response = await client.post(
                introspection_endpoint,
                data={
                    'token': auth_token,
                    'token_type_hint': 'access_token'
                },
                auth=(client_id, client_secret),
                headers={'Content-Type': 'application/x-www-form-urlencoded'}
            )
            response.raise_for_status()

            introspection_data = response.json()
            logger.debug(f"Introspection response: {introspection_data}")

            # Check if token is active
            if not introspection_data.get("active", False):
                raise ValueError("Token is not active")
            
            # If token is active

            # Extract user information from introspection response
            username = introspection_data.get("username", "")
            client_id = introspection_data.get("client_id", "")

            # Use username as subject
            sub = username.split('/', 1)[1]

            # Extract expiration
            exp = introspection_data.get("exp")

            # Extract organization information from username
            org_handle = ""

            if "@" in username:
                parts = username.split("@")
                if len(parts) > 1:
                    org_handle = parts[-1]

            return AuthContext(
                auth_token=auth_token,
                organization=Organization(handle=org_handle),
                sub=sub,
                exp=exp
            )

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during token introspection: {str(e)}")
            raise ValueError(f"Token introspection failed: {str(e)}")
        except Exception as e:
            logger.error(f"Error during token introspection: {str(e)}")
            raise ValueError(f"Token validation failed: {str(e)}")
