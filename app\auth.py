import logging

import httpx

from app.config import get_config, get_secret
from app.constants import CONTENT_TYPE_HEADER, CONTENT_TYPE_FORM_URLENCODED
from app.exceptions import TokenValidationError, TokenIntrospectionError
from app.model import AuthContext, Organization

# Load configuration
config = get_config()

logger = logging.getLogger(__name__)


async def introspect_opaque_token(auth_token: str) -> AuthContext:
    """
    Introspect an opaque token using Asgardeo's introspection endpoint.

    Args:
        auth_token: The opaque access token to validate

    Returns:
        AuthContext: Authentication context with user information

    Raises:
        TokenValidationError: If token is invalid or validation fails
        TokenIntrospectionError: If introspection service fails
    """
    # Construct introspection endpoint using organization
    introspection_endpoint = f"https://api.asgardeo.io/t/{config.token_validation.organization}/oauth2/introspect"
    client_id = config.token_validation.client_id
    client_secret = get_secret(config.token_validation.client_secret_file_path)

    async with httpx.AsyncClient(timeout=httpx.Timeout(config.token_validation.token_introspection_timeout)) as client:
        try:
            response = await client.post(
                introspection_endpoint,
                data={
                    'token': auth_token,
                    'token_type_hint': 'access_token'
                },
                auth=(client_id, client_secret),
                headers={CONTENT_TYPE_HEADER: CONTENT_TYPE_FORM_URLENCODED}
            )
            response.raise_for_status()

            introspection_data = response.json()
            logger.debug(f"Introspection response: {introspection_data}")

            # Check if token is active.
            if not introspection_data.get("active", False):
                raise TokenValidationError("Token is not active")

            # If token is active.

            # Extract user information from introspection response.
            username = introspection_data.get("username", "")
            client_id = introspection_data.get("client_id", "")

            # Use username as subject.
            sub = username.split('/', 1)[1]

            # Extract expiration.
            exp = introspection_data.get("exp")

            # Extract organization information from username.
            org_handle = ""

            if "@" in username:
                parts = username.split("@")
                if len(parts) > 1:
                    org_handle = parts[-1]

            return AuthContext(
                auth_token=auth_token,
                organization=Organization(handle=org_handle),
                sub=sub,
                exp=exp
            )

        except httpx.HTTPStatusError as e:
            # HTTP status errors (4xx, 5xx)
            if e.response.status_code >= 500:
                logger.error(f"Server error during token introspection: {str(e)}")
                raise TokenIntrospectionError(f"Authentication service error: {str(e)}")
            else:
                logger.warning(f"Client error during token introspection: {str(e)}")
                raise TokenValidationError(f"Token validation failed: {str(e)}")
        except httpx.RequestError as e:
            # Network/connection errors
            logger.error(f"Network error during token introspection: {str(e)}")
            raise TokenIntrospectionError(f"Authentication service unavailable: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error during token introspection: {str(e)}")
            raise TokenValidationError(f"Token validation failed: {str(e)}")
