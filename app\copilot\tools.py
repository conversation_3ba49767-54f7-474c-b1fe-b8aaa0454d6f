import logging
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any
from typing import AsyncGenerator, Optional, Union

import httpx
from langchain.pydantic_v1 import Field, BaseModel as LangChainBaseModel
from pydantic import BaseModel

from app.copilot.clients import make_request, make_stream_request, docs_assistant_url
from app.copilot.constants import QUESTIONS_KEY, HISTORY_KEY
from app.model import AuthContext, CacheRecord


class CallInitializationMessage(BaseModel):
    call_id: str
    tool: str
    reason: str
    question: str


class StatusEnum(str, Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    STREAM = "STREAM"


class CallCompletionMessage(BaseModel):
    call_id: str
    content: str
    status: StatusEnum


class ToolContext(BaseModel):
    auth_context: AuthContext
    session_id: str
    original_question: str
    current_datetime: str
    history: CacheRecord
    llm: Any


class ToolOutput(BaseModel):
    tool_name: str
    tool_call_id: str
    content: Union[str, BaseModel]
    status: Optional[StatusEnum] = StatusEnum.SUCCESS


docs_assistant_description = """This assistant is an expert on the Asgardeo Identity as a Service (IDaaS) platform and all its capabilities. By referring to comprehensive Asgardeo documentation, it provides detailed guidance on how to use Asgardeo features and accomplish specific identity management tasks through the Asgardeo Console. This includes user management, authentication, authorization, single sign-on (SSO), multi-factor authentication (MFA), identity federation, application integration, and security configurations. It offers step-by-step instructions to help users implement identity solutions effectively and troubleshoot any issues they encounter."""  # noqa: E501

class BaseTool(LangChainBaseModel, ABC):
    reason: str = Field(..., description="Explain the intention of the user which justifies the use of this tool "
                                         "based on the user's question and the conversational history.")

    async def run(self, tool_call_id: str, context: ToolContext, is_stream=False) -> AsyncGenerator[ToolOutput, None]:
        async def get_output() -> AsyncGenerator[Any, None]:
            try:
                if is_stream:  # stream the tool
                    async for result in self._stream(tool_call_id, context):
                        yield result
                else:  # run tool at once
                    async for result in self._run(tool_call_id, context):
                        yield result
            except httpx.ReadTimeout:
                raise TimeoutError(f"Timeout during retrieving the answer for {self.__class__.__name__}.")

        try:
            async for output in get_output():
                if not isinstance(output, ToolOutput):
                    raise ValueError(f"Unexpected response from {self.__class__.__name__} client")
                yield output
        except Exception as e:
            logging.error(f"{context.session_id} - Failed to execute the {self.__class__.__name__}", exc_info=True)
            yield ToolOutput(
                tool_name=self.__class__.__name__,
                tool_call_id=tool_call_id,
                content=f"Unable to execute the {self.__class__.__name__} at the moment due to: '{str(e)}'.",
                status=StatusEnum.FAILURE
            )

    async def message(self, tool_call_id: str) -> CallInitializationMessage:
        return CallInitializationMessage(call_id=tool_call_id,
                                         tool=await self._get_message(),
                                         reason=self.reason if hasattr(self, "reason") else "None",
                                         question=self.command)

    @abstractmethod
    async def _stream(self, tool_call_id: str, context: ToolContext) -> AsyncGenerator[Any, None]:
        yield None

    @abstractmethod
    async def _run(self, tool_call_id: str, context: ToolContext) -> AsyncGenerator[Any, None]:
        yield None

    @abstractmethod
    async def _get_message(self) -> str:
        pass


class AsgardeoDocsAssistant(BaseTool):
    f"""{docs_assistant_description}"""

    command: str = Field(...,
                         description="Ask a specific, detailed question about Asgardeo identity management features, configurations, or how to accomplish a particular identity-related task. Be precise to get the most relevant information from Asgardeo documentation.",
                         min_length=1)

    async def _run(self, tool_call_id: str, context: ToolContext, is_stream=False) -> AsyncGenerator[Any, None]:
        content = await make_request(f"{docs_assistant_url}/chat",
                                     context.session_id,
                                     body={QUESTIONS_KEY: [f"{context.original_question} ({self.command})"],
                                           HISTORY_KEY: [record.__dict__ for record in context.history.history]})

        if isinstance(content, str):
            yield ToolOutput(tool_name=self.__class__.__name__,
                             tool_call_id=tool_call_id,
                             content=content)
            return

        yield content

    async def _stream(self, tool_call_id: str, context: ToolContext) -> AsyncGenerator[Any, None]:
        async for chunk in make_stream_request(f"{docs_assistant_url}/stream",
                                               context.session_id,
                                               json={QUESTIONS_KEY: [self.command],
                                                     HISTORY_KEY: [record.__dict__ for record in
                                                                   context.history.history]}):

            if not isinstance(chunk, str):
                yield chunk
                return
            yield ToolOutput(tool_name=self.__class__.__name__,
                             tool_call_id=tool_call_id,
                             content=chunk,
                             status=StatusEnum.STREAM)

    async def _get_message(self) -> str:
        return "Checking Asgardeo Documentation"
