# Assistant service URLs and keys.
DOCS_ASSISTANT_URL_KEY = "DOCS_ASSISTANT_URL"
COPILOT_APP_CLIENT_ID_KEY = "COPILOT_APP_CLIENT_ID"
COPILOT_APP_CLIENT_SECRET_KEY = "COPILOT_APP_CLIENT_SECRET"
COPILOT_APP_TOKEN_ENDPOINT_KEY = "COPILOT_APP_TOKEN_ENDPOINT"

# Assistant input schema keys.
QUESTIONS_KEY = "questions"
HISTORY_KEY = "history"
REASON_KEY = "reason"

# HTTP methods.
POST_METHOD = "POST"

# Copilot agent constants.
INPUT_QUESTION_KEY = "question"
INPUT_CONVERSATION_HISTORY_KEY = "history"
MAX_RETRY_ATTEMPTS = 2

# Memory constants.
MAX_HISTORY_PROMPT_SIZE = 2000
