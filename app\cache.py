import json
import logging
import os
import datetime
from dotenv import load_dotenv

from azure.cosmos.aio import CosmosClient

from app.constants import COPILOT_NAMESPACE, CONVERSATION_CACHE_TTL, \
    MAX_NUM_HISTORY_RECORDS, COSMOS_DB_ENDPOINT, COSMOS_DB_KEY, \
    COSMOS_DB_DATABASE, COSMOS_DB_CONTAINER
from app.model import CacheRecord, HistoryRecord
from app.utils import retry_manager

load_dotenv()

logger = logging.getLogger(__name__)

# Cosmos DB configuration and client initialization
cosmos_endpoint = os.environ.get(COSMOS_DB_ENDPOINT)
cosmos_key = os.environ.get(COSMOS_DB_KEY)
cosmos_database = os.environ.get(COSMOS_DB_DATABASE)
cosmos_container = os.environ.get(COSMOS_DB_CONTAINER)

# Initialize Cosmos DB client
cosmos_client = None
cosmos_database_client = None
cosmos_container_client = None

try:
    # Initialize Cosmos DB client
    if cosmos_endpoint and cosmos_key and cosmos_database and cosmos_container:
        cosmos_client = CosmosClient(cosmos_endpoint, credential=cosmos_key)
        cosmos_database_client = cosmos_client.get_database_client(cosmos_database)
        cosmos_container_client = cosmos_database_client.get_container_client(cosmos_container)
        logger.info(f"Cosmos DB client initialized for database {cosmos_database} and container {cosmos_container}")
    else:
        logger.warning("Cosmos DB configuration is incomplete. Chat history will not be available.")
        logger.warning(f"Missing configuration: endpoint={not cosmos_endpoint}, key={not cosmos_key}, database={not cosmos_database}, container={not cosmos_container}")
except Exception as e:
    logger.error(f"Failed to initialize Cosmos DB client: {e}", exc_info=True)


class CacheRecordJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, CacheRecord):
            return obj.model_dump()
        return super().default(obj)


async def _retrieve_history(session_id: str) -> CacheRecord:
    """Retrieve conversation history from Cosmos DB"""
    try:
        # Check if Cosmos DB client is available
        if not cosmos_container_client:
            logger.warning(f"Cosmos DB client not available. Cannot retrieve history for session {session_id}")
            return CacheRecord(history=[], summary="")

        # Query for the document with the given session_id using parameterized query
        query = "SELECT * FROM c WHERE c.id = @session_id"
        items = []
        async for item in cosmos_container_client.query_items(
            query=query,
            parameters=[
                {"name": "@session_id", "value": session_id}
            ]
        ):
            items.append(item)

        # If no document found, return empty history
        if not items:
            logger.info(f"No history found for session {session_id}")
            return CacheRecord(history=[], summary="")

        # Get the document
        doc = items[0]

        # Convert the document to CacheRecord
        history_records = []
        for record in doc.get('history', []):
            history_records.append(HistoryRecord(
                question=record.get('question', ''),
                answer=record.get('answer', '')
            ))

        return CacheRecord(
            history=history_records,
            summary=doc.get('summary', '')
        )
    except Exception as e:
        logger.error(f"Error retrieving history from Cosmos DB: {e}", exc_info=True)
        return CacheRecord(history=[], summary="")


async def _update_history(session_id: str, history: CacheRecord):
    """Update conversation history in Cosmos DB"""
    try:
        # Check if Cosmos DB client is available
        if not cosmos_container_client:
            logger.warning(f"Cosmos DB client not available. Cannot update history for session {session_id}")
            return

        # Truncate the history to store last 10 records
        history = CacheRecord(
            history=history.history[-MAX_NUM_HISTORY_RECORDS:],
            summary=history.summary
        )

        # Get current time
        current_time = datetime.datetime.now(datetime.timezone.utc)

        # For conversation history, we want to reset the TTL when new questions are asked
        doc = {
            'id': session_id,
            'session_id': session_id,
            'namespace': COPILOT_NAMESPACE,
            'history': [record.model_dump() for record in history.history],
            'summary': history.summary,
            'ttl': CONVERSATION_CACHE_TTL,
            'created_time': current_time.isoformat(),
            'last_updated': current_time.isoformat(),
            'expiry_time': (current_time + datetime.timedelta(seconds=CONVERSATION_CACHE_TTL)).isoformat()
        }

        # Upsert the document (create if not exists, update if exists)
        await cosmos_container_client.upsert_item(doc)
        logger.info(f"Updated history in Cosmos DB for session {session_id}, TTL: {CONVERSATION_CACHE_TTL} seconds")
    except Exception as e:
        logger.error(f"Error updating history in Cosmos DB: {e}", exc_info=True)


async def fetch_cache(session_id: str) -> CacheRecord:
    try:
        history = await retry_manager(_retrieve_history, session_id)
    except Exception as e:
        # if cache is failing let's work without history
        logger.error(f"Error while restoring the history: {e}, id = {session_id}")
        history = CacheRecord(history=[], summary="")
    return history


async def update_cache(session_id: str, history: CacheRecord):
    try:
        await retry_manager(_update_history, session_id, history)
    except Exception:
        logging.error(f"{session_id} - Error occurred while updating cache. Copilot is working without memory",
                      exc_info=True)


async def ping() -> bool:
    """Ping the cache to check if it's available"""
    try:
        if not cosmos_container_client:
            logger.warning("Cosmos DB client not available. Cannot ping Cosmos DB.")
            return False

        # Try to query a single document to check if Cosmos DB is available
        query = "SELECT TOP 1 * FROM c"
        async for _ in cosmos_container_client.query_items(
            query=query
        ):
            pass  # Just checking if the query works
        logger.info("Successfully pinged Cosmos DB")
        return True
    except Exception as e:
        logger.error(f"Error while pinging the cache: {e}", exc_info=True)
        return False
