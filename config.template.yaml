#  Copyright (c) 2024, WSO2 LLC. (http://www.wso2.com).
#
#  This software is the property of WSO2 LLC. and its suppliers, if any.
#  Dissemination of any information or reproduction of any material contained
#  herein in any form is strictly forbidden, unless permitted by WSO2 expressly.
#  You may not alter or remove any copyright or other notice from copies of this content.

# Template configuration file for Asgardeo AI Copilot
# Copy this file to config.yaml and update with your actual values
# Create corresponding files in configs/ directory with your actual values

azureOpenAiApi:
  apiType: "azure"
  apiVersion: "2025-01-01-preview"  # Update with your API version
  azureOpenAiEndpoint: "https://your-account.openai.azure.com/"  # Update with your endpoint
  apiKeyFilePath: "configs/azureOpenAIConfigs/apiKey/azureAPIKey"  # Path to API key file

  llmDeployments:
    - name: "primaryModel"
      deploymentName: "your-primary-deployment"  # Update with your deployment name
      modelName: "gpt-4o"  # Update with your model name
      modelVersion: "2024-11-20"  # Update with your model version
      temperature: 0.7
      maxRetries: 0

    - name: "secondaryModel"
      deploymentName: "your-secondary-deployment"  # Update with your deployment name
      modelName: "gpt-4o"  # Update with your model name
      modelVersion: "2024-11-20"  # Update with your model version
      temperature: 0.7
      maxRetries: 0

userQuery:
  wordLimit: 150
  maxQuestionLength: 256

tokenValidation:
  enabled: true
  organizationFilePath: "configs/asgardeoApplication/organization"  # Path to organization file
  clientIdFilePath: "configs/asgardeoApplication/clientId"  # Path to client ID file
  clientSecretFilePath: "configs/asgardeoApplication/clientSecret"  # Path to client secret file
  hostNameVerification: false
  tokenIntrospectionTimeout: 2

copilotOAuth2:
  organizationFilePath: "configs/copilotApplication/organization"  # Path to organization file
  clientIdFilePath: "configs/copilotApplication/clientId"  # Path to client ID file
  clientSecretFilePath: "configs/copilotApplication/clientSecret"  # Path to client secret file

tools:
  docsAssistant:
    enabled: true
    url: "https://your-docs-assistant-endpoint/docs-assistant"  # Update with your endpoint

cosmosDb:
  connectionStringFilePath: "configs/cosmosDbConfigs/connectionString"  # Path to connection string file
  databaseName: "your-database-name"  # Update with your database name
  containerName: "conversation-history"
  conversationCacheTtl: 3600
  maxHistoryRecords: 10

cors:
  allowedOrigins: "*"  # Update for production (specific origins)

logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

retry:
  maxAttempts: 2

constants:
  copilotNamespace: "copilot"
  secretDirPath: "/mnt/csi/secret-ai-copilot"  # Update for your environment
  correlationIdHeaderName: "correlation-id"
  requestIdHeaderName: "x-request-id"
