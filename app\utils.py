import asyncio
import logging
import os

import openai

from app.constants import MAX_RETRY_ATTEMPTS


def get_secret(file_dir, secret_name) -> str:
    secret = os.environ.get(secret_name)
    if not secret:
        try:
            with open(os.path.join(file_dir, secret_name), 'r') as secret_file:
                secret = secret_file.read()
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Secret file '{secret_name}' not found") from e
        except <PERSON><PERSON>rro<PERSON> as e:
            raise IOError(f"Error reading secret from file '{secret_name}'") from e
    if not secret:
        raise ValueError(f"Secret value in file '{secret_name}' is empty")
    return secret


async def retry_manager(func, *args):
    max_retries = MAX_RETRY_ATTEMPTS
    sleep = 1
    for i in range(max_retries):
        try:
            return await func(*args)
        except Exception as e:
            if isinstance(e, openai.AuthenticationError):  # skip retrying if authentication fails
                raise e
            if i == max_retries - 1:
                raise Exception(f"{func.__name__} failed after {max_retries} attempts.")
            else:
                logging.warning(
                    f"Error occurred while executing {func.__name__}. Retrying... Attempt {i + 1}/{max_retries}")
                sleep = sleep * 2
                await asyncio.sleep(sleep)
