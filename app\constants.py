# Secret constants.
SECRET_DIR_PATH = "/mnt/csi/secret-ai-copilot"

# Chat history constants.
CONVERSATION_CACHE_TTL = 3600 # 3600 seconds = 1 hour.
COPILOT_NAMESPACE = "copilot"
MAX_NUM_HISTORY_RECORDS = 10

# Cosmos DB constants.
COSMOS_DB_CONNECTION_STRING = "COSMOS_DB_CONNECTION_STRING"
COSMOS_DB_DATABASE = "COSMOS_DB_DATABASE"
COSMOS_DB_CONTAINER = "COSMOS_DB_CONTAINER"

# Azure constants.
AZURE_OPENAI_API_KEY = "AZURE_OPENAI_API_KEY"
AZURE_OPENAI_SERVICE_URL = "AZURE_OPENAI_SERVICE_URL"
AZURE_OPENAI_API_VERSION = "AZURE_OPENAI_API_VERSION"
AZURE_OPENAI_DEPLOYMENT_NAME = "AZURE_OPENAI_DEPLOYMENT_NAME"
AZURE_OPENAI_MAX_RETRIES = "AZURE_OPENAI_MAX_RETRIES"

# Tiktoken constants.
TIKTOKEN_MODEL_NAME = "TIKTOKEN_MODEL_NAME"

# Opaque token authentication constants.
ASGARDEO_INTROSPECTION_ENDPOINT = "ASGARDEO_INTROSPECTION_ENDPOINT"
ASGARDEO_INTROSPECTION_CLIENT_ID = "ASGARDEO_INTROSPECTION_CLIENT_ID"
ASGARDEO_INTROSPECTION_CLIENT_SECRET = "ASGARDEO_INTROSPECTION_CLIENT_SECRET"

# Service constants.
X_REQUEST_ID = "x-request-id"
CORRELATION_ID = "correlation-id"

# Assistant enabling flags.
DOCS_ASSISTANT_ENABLED = "DOCS_ASSISTANT_ENABLED"

# Retry manager.
MAX_RETRY_ATTEMPTS = 2

# Question input validation.
MAX_QUESTION_LENGTH = "MAX_QUESTION_LENGTH"

# HTTP Authentication constants.
BEARER_SCHEME = "Bearer"
WWW_AUTHENTICATE_HEADER = "WWW-Authenticate"
AUTHORIZATION_HEADER = "Authorization"
CONTENT_TYPE_HEADER = "Content-Type"
CONTENT_TYPE_FORM_URLENCODED = "application/x-www-form-urlencoded"
